import {
  Transaction,
  AnalysisResponse,
  ApiResponse,
  TransactionStats,
  JobData,
  ProcessingLog,
  AuditTrail,
  BatchMetadata,
  ErrorLog,
  PerformanceLog
} from '../types';
import { supabase } from '../../supabaseClient';
import { authService } from './auth';
import { loggingService } from './logging-service';

const API_BASE_URL = '/api/v1';

class ApiService {
  // Helper to get a fresh, valid token
  private async getValidToken(): Promise<string | null> {
    try {
      // Get current session from Supabase
      const { data: { session }, error } = await supabase.auth.getSession();

      if (error || !session) {
        console.warn('[API] No valid session found:', error?.message);
        localStorage.removeItem('authToken');
        return null;
      }

      // Check if token is about to expire (within 5 minutes)
      const expiresAt = session.expires_at ? session.expires_at * 1000 : 0;
      const now = Date.now();
      const fiveMinutes = 5 * 60 * 1000;

      if (expiresAt - now < fiveMinutes) {
        console.log('[API] Token expiring soon, refreshing...');
        // Refresh the session
        const { data: { session: refreshedSession }, error: refreshError } = await supabase.auth.refreshSession();

        if (refreshError || !refreshedSession) {
          console.error('[API] Failed to refresh session:', refreshError?.message);
          localStorage.removeItem('authToken');
          return null;
        }

        localStorage.setItem('authToken', refreshedSession.access_token);
        return refreshedSession.access_token;
      }

      // Token is still valid
      localStorage.setItem('authToken', session.access_token);
      return session.access_token;
    } catch (error) {
      console.error('[API] Error getting valid token:', error);
      localStorage.removeItem('authToken');
      return null;
    }
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;
    let token = localStorage.getItem('authToken');

    // Try to get a fresh token if user is authenticated
    if (authService.isAuthenticated()) {
      token = await this.getValidToken();
    }

    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    if (token) {
      config.headers = {
        ...config.headers,
        Authorization: `Bearer ${token}`,
      };
    }

    const response = await fetch(url, config);

    if (!response.ok) {
      // If unauthorized, clear token and sign out user
      if (response.status === 401) {
        console.warn('[API] Unauthorized request, clearing auth state');
        localStorage.removeItem('authToken');
        // Optionally sign out the user
        await supabase.auth.signOut();
        throw new Error('Invalid or expired token. Please log in again.');
      }

      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  // Analysis endpoints
  async analyzeText(text: string, analysisMode: 'basic' | 'advanced' = 'advanced'): Promise<AnalysisResponse> {
    return this.request<AnalysisResponse>('/analyze/text', {
      method: 'POST',
      body: JSON.stringify({ text, analysisMode }),
    });
  }

  async analyzeImage(file: File, analysisMode: 'basic' | 'advanced' = 'advanced'): Promise<AnalysisResponse> {
    const formData = new FormData();
    formData.append('image', file);
    formData.append('analysisMode', analysisMode);

    // Get fresh token
    const token = await this.getValidToken();
    if (!token) {
      throw new Error('Authentication required. Please log in.');
    }

    const response = await fetch(`${API_BASE_URL}/analyze/image`, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: formData,
    });

    if (!response.ok) {
      if (response.status === 401) {
        localStorage.removeItem('authToken');
        await supabase.auth.signOut();
        throw new Error('Invalid or expired token. Please log in again.');
      }
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  // Document processing endpoints
  async processDocument(formData: FormData): Promise<ApiResponse> {
    const trackingId = loggingService.startPerformanceTracking('document_processing');

    try {
      // Get fresh token
      const token = await this.getValidToken();
      if (!token) {
        throw new Error('Authentication required. Please log in.');
      }

      // Extract file information for logging
      const file = formData.get('document') as File;

      if (file) {
        await loggingService.logFileUpload(
          file.name,
          file.size,
          file.type,
          0, // Duration will be updated later
          true
        );
      }

      const response = await fetch(`${API_BASE_URL}/documents/process`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
        },
        body: formData,
      });

      // Log API call performance
      await loggingService.logApiCall(
        '/documents/process',
        'POST',
        0, // duration placeholder, update if you have the actual duration
        response.status
      );

      if (!response.ok) {
        if (response.status === 401) {
          localStorage.removeItem('authToken');
          await supabase.auth.signOut();
          throw new Error('Invalid or expired token. Please log in again.');
        }
        const errorData = await response.json().catch(() => ({}));

        // Log the error
        await loggingService.logError(
          'DocumentProcessingError',
          errorData.error || `HTTP error! status: ${response.status}`,
          {
          }
        );

        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      await loggingService.endPerformanceTracking(trackingId, {
        success: true,
        status: response.status
      });

      return response.json();
    } catch (error) {
      await loggingService.logError(
        'DocumentProcessingException',
        error instanceof Error ? error.message : 'Unknown error',
        {
        },
        error instanceof Error ? error.stack : undefined
      );

      await loggingService.endPerformanceTracking(trackingId, {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      throw error;
    }
  }

  // Job status endpoints
  async getJobStatus(jobId: string): Promise<ApiResponse> {
    return this.request<ApiResponse>(`/job/${jobId}`);
  }

  async getUserJobs(userId: string, limit: number = 20): Promise<ApiResponse> {
    return this.request<ApiResponse>(`/jobs/${userId}?limit=${limit}`);
  }

  async cancelJob(jobId: string): Promise<ApiResponse> {
    return this.request<ApiResponse>(`/job/${jobId}`, {
      method: 'DELETE',
    });
  }

  async getQueueStats(): Promise<ApiResponse> {
    return this.request<ApiResponse>('/queue/stats');
  }

  async getProcessingHistory(): Promise<ApiResponse> {
    return this.request<ApiResponse>('/documents/history');
  }

  async validateExtractedData(extractedData: any): Promise<ApiResponse> {
    return this.request<ApiResponse>('/documents/validate', {
      method: 'POST',
      body: JSON.stringify({ extractedData }),
    });
  }

  async getProcessingStats(): Promise<ApiResponse> {
    return this.request<ApiResponse>('/documents/stats');
  }

  // Transaction endpoints
  async getTransactions(): Promise<ApiResponse<Transaction[]>> {
    return this.request<ApiResponse<Transaction[]>>('/transactions');
  }

  async updateTransaction(id: string, updates: Partial<Transaction>): Promise<ApiResponse<Transaction>> {
    return this.request<ApiResponse<Transaction>>(`/transactions/${id}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
  }

  async deleteTransaction(id: string): Promise<ApiResponse> {
    return this.request<ApiResponse>(`/transactions/${id}`, {
      method: 'DELETE',
    });
  }

  async getTransactionStats(startDate?: string, endDate?: string): Promise<ApiResponse<TransactionStats>> {
    const params = new URLSearchParams();
    if (startDate) params.append('startDate', startDate);
    if (endDate) params.append('endDate', endDate);

    return this.request<ApiResponse<TransactionStats>>(`/transactions/stats?${params.toString()}`);
  }

  async autoCategorizeTransactions(transactionIds: string[]): Promise<ApiResponse<Transaction[]>> {
    return this.request<ApiResponse<Transaction[]>>('/transactions/autocategorize', {
      method: 'POST',
      body: JSON.stringify({ transactionIds }),
    });
  }

  // Data management endpoints
  async deleteAllData(): Promise<ApiResponse> {
    return this.request<ApiResponse>('/data/delete-all', {
      method: 'DELETE',
    });
  }

  async exportData(): Promise<any> {
    return this.request<any>('/data/export');
  }

  // New endpoints for migrated database schema

  // Processing logs endpoints
  async getProcessingLogs(userId?: string, limit: number = 50): Promise<ApiResponse<ProcessingLog[]>> {
    const params = new URLSearchParams();
    if (userId) params.append('userId', userId);
    params.append('limit', limit.toString());

    return this.request<ApiResponse<ProcessingLog[]>>(`/processing-logs?${params.toString()}`);
  }

  async createProcessingLog(logData: Omit<ProcessingLog, 'id' | 'created_at'>): Promise<ApiResponse<ProcessingLog>> {
    return this.request<ApiResponse<ProcessingLog>>('/processing-logs', {
      method: 'POST',
      body: JSON.stringify(logData),
    });
  }

  // Audit trail endpoints
  async getAuditTrail(userId?: string, limit: number = 50): Promise<ApiResponse<AuditTrail[]>> {
    const params = new URLSearchParams();
    if (userId) params.append('userId', userId);
    params.append('limit', limit.toString());

    return this.request<ApiResponse<AuditTrail[]>>(`/audit-trail?${params.toString()}`);
  }

  async createAuditEntry(auditData: Omit<AuditTrail, 'id' | 'created_at'>): Promise<ApiResponse<AuditTrail>> {
    return this.request<ApiResponse<AuditTrail>>('/audit-trail', {
      method: 'POST',
      body: JSON.stringify(auditData),
    });
  }

  // Batch metadata endpoints
  async getBatchMetadata(userId?: string, limit: number = 20): Promise<ApiResponse<BatchMetadata[]>> {
    const params = new URLSearchParams();
    if (userId) params.append('userId', userId);
    params.append('limit', limit.toString());

    return this.request<ApiResponse<BatchMetadata[]>>(`/batch-metadata?${params.toString()}`);
  }

  async createBatchMetadata(batchData: Omit<BatchMetadata, 'id' | 'created_at' | 'updated_at'>): Promise<ApiResponse<BatchMetadata>> {
    return this.request<ApiResponse<BatchMetadata>>('/batch-metadata', {
      method: 'POST',
      body: JSON.stringify(batchData),
    });
  }

  async updateBatchMetadata(id: string, updates: Partial<BatchMetadata>): Promise<ApiResponse<BatchMetadata>> {
    return this.request<ApiResponse<BatchMetadata>>(`/batch-metadata/${id}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
  }

  // Error logs endpoints
  async getErrorLogs(userId?: string, limit: number = 50): Promise<ApiResponse<ErrorLog[]>> {
    const params = new URLSearchParams();
    if (userId) params.append('userId', userId);
    params.append('limit', limit.toString());

    return this.request<ApiResponse<ErrorLog[]>>(`/error-logs?${params.toString()}`);
  }

  async createErrorLog(errorData: Omit<ErrorLog, 'id' | 'created_at'>): Promise<ApiResponse<ErrorLog>> {
    return this.request<ApiResponse<ErrorLog>>('/error-logs', {
      method: 'POST',
      body: JSON.stringify(errorData),
    });
  }

  // Performance logs endpoints
  async getPerformanceLogs(operation?: string, limit: number = 100): Promise<ApiResponse<PerformanceLog[]>> {
    const params = new URLSearchParams();
    if (operation) params.append('operation', operation);
    params.append('limit', limit.toString());

    return this.request<ApiResponse<PerformanceLog[]>>(`/performance-logs?${params.toString()}`);
  }

  async createPerformanceLog(perfData: Omit<PerformanceLog, 'id' | 'created_at'>): Promise<ApiResponse<PerformanceLog>> {
    return this.request<ApiResponse<PerformanceLog>>('/performance-logs', {
      method: 'POST',
      body: JSON.stringify(perfData),
    });
  }

  // Enhanced job status endpoints with new schema
  async getJobStatusEnhanced(jobId: string): Promise<ApiResponse<JobData>> {
    return this.request<ApiResponse<JobData>>(`/jobs/${jobId}`);
  }

  async getUserJobsEnhanced(userId: string, limit: number = 20): Promise<ApiResponse<JobData[]>> {
    return this.request<ApiResponse<JobData[]>>(`/users/${userId}/jobs?limit=${limit}`);
  }

  async updateJobStatus(jobId: string, updates: Partial<JobData>): Promise<ApiResponse<JobData>> {
    return this.request<ApiResponse<JobData>>(`/jobs/${jobId}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
  }
}

export const apiService = new ApiService(); 