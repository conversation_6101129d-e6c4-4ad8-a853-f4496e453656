import { supabase } from '../../supabaseClient';
import { User } from '../types';
import type { User as SupabaseUser, Session } from '@supabase/supabase-js';

class AuthService {
  private currentUser: User | null = null;
  private authStateListeners: ((user: User | null) => void)[] = [];

  constructor() {
    // Initialize auth state from Supabase session
    this.initializeAuthState();

    // Listen for auth state changes
    supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('[Auth] Auth state changed:', event, session?.user?.id);

      if (session?.user) {
        this.currentUser = this.convertSupabaseUser(session.user);
        // Store the access token
        localStorage.setItem('authToken', session.access_token);
      } else {
        this.currentUser = null;
        localStorage.removeItem('authToken');
      }

      // Notify all listeners
      this.authStateListeners.forEach(callback => callback(this.currentUser));
    });
  }

  private async initializeAuthState(): Promise<void> {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      if (error) {
        console.error('[Auth] Error getting session:', error);
        return;
      }

      if (session?.user) {
        this.currentUser = this.convertSupabaseUser(session.user);
        localStorage.setItem('authToken', session.access_token);
      }
    } catch (error) {
      console.error('[Auth] Error initializing auth state:', error);
    }
  }

  private convertSupabaseUser(supabaseUser: SupabaseUser): User {
    return {
      uid: supabaseUser.id,
      email: supabaseUser.email || '',
      displayName: supabaseUser.user_metadata?.full_name || supabaseUser.user_metadata?.name || undefined,
      photoURL: supabaseUser.user_metadata?.avatar_url || supabaseUser.user_metadata?.picture || undefined,
    };
  }

  async signInWithGoogle(): Promise<User> {
    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: window.location.origin
        }
      });

      if (error) {
        console.error('Google sign-in error:', error);
        throw new Error(error.message);
      }

      // For OAuth, the user will be redirected and auth state will be updated via the listener
      // Return current user or throw if not available
      if (this.currentUser) {
        return this.currentUser;
      }

      throw new Error('Authentication in progress. Please wait for redirect.');
    } catch (error) {
      console.error('Google sign-in error:', error);
      throw error;
    }
  }

  async signOut(): Promise<void> {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) {
        console.error('Sign-out error:', error);
        throw new Error(error.message);
      }
    } catch (error) {
      console.error('Sign-out error:', error);
      throw error;
    }
  }

  async signUpWithEmail(email: string, password: string): Promise<User> {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
      });

      if (error) {
        console.error('Email sign-up error:', error);
        throw new Error(error.message);
      }

      if (!data.user) {
        throw new Error('Sign-up failed: No user returned');
      }

      return this.convertSupabaseUser(data.user);
    } catch (error) {
      console.error('Email sign-up error:', error);
      throw error;
    }
  }

  async signInWithEmail(email: string, password: string): Promise<User> {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        console.error('Email sign-in error:', error);
        throw new Error(error.message);
      }

      if (!data.user) {
        throw new Error('Sign-in failed: No user returned');
      }

      return this.convertSupabaseUser(data.user);
    } catch (error) {
      console.error('Email sign-in error:', error);
      throw error;
    }
  }

  getCurrentUser(): User | null {
    return this.currentUser;
  }

  isAuthenticated(): boolean {
    return this.currentUser !== null;
  }

  // Get current session
  async getSession(): Promise<Session | null> {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      if (error) {
        console.error('[Auth] Error getting session:', error);
        return null;
      }
      return session;
    } catch (error) {
      console.error('[Auth] Error getting session:', error);
      return null;
    }
  }

  // Get current access token
  async getAccessToken(): Promise<string | null> {
    try {
      const session = await this.getSession();
      return session?.access_token || null;
    } catch (error) {
      console.error('[Auth] Error getting access token:', error);
      return null;
    }
  }

  // Refresh session
  async refreshSession(): Promise<Session | null> {
    try {
      const { data: { session }, error } = await supabase.auth.refreshSession();
      if (error) {
        console.error('[Auth] Error refreshing session:', error);
        return null;
      }
      return session;
    } catch (error) {
      console.error('[Auth] Error refreshing session:', error);
      return null;
    }
  }

  // Listen for auth state changes
  onAuthStateChanged(callback: (user: User | null) => void): () => void {
    // Add callback to listeners
    this.authStateListeners.push(callback);

    // Call immediately with current user
    callback(this.currentUser);

    // Return unsubscribe function
    return () => {
      const index = this.authStateListeners.indexOf(callback);
      if (index > -1) {
        this.authStateListeners.splice(index, 1);
      }
    };
  }

  // Reset password
  async resetPassword(email: string): Promise<void> {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`
      });

      if (error) {
        console.error('Password reset error:', error);
        throw new Error(error.message);
      }
    } catch (error) {
      console.error('Password reset error:', error);
      throw error;
    }
  }

  // Update password
  async updatePassword(newPassword: string): Promise<void> {
    try {
      const { error } = await supabase.auth.updateUser({
        password: newPassword
      });

      if (error) {
        console.error('Password update error:', error);
        throw new Error(error.message);
      }
    } catch (error) {
      console.error('Password update error:', error);
      throw error;
    }
  }
}

export const authService = new AuthService(); 