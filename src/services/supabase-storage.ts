/**
 * Supabase Storage Service
 * 
 * This service provides file upload, download, and management functionality
 * using Supabase Storage, replacing Firebase Storage.
 */

import { supabase } from '../../supabaseClient';

export interface UploadResult {
  success: boolean;
  url?: string;
  path?: string;
  error?: string;
}

export interface FileMetadata {
  name: string;
  size: number;
  type: string;
  lastModified: number;
}

export interface StorageFile {
  name: string;
  id: string;
  updated_at: string;
  created_at: string;
  last_accessed_at: string;
  metadata: {
    eTag: string;
    size: number;
    mimetype: string;
    cacheControl: string;
    lastModified: string;
    contentLength: number;
    httpStatusCode: number;
  };
}

class SupabaseStorageService {
  private readonly DOCUMENTS_BUCKET = 'documents';
  private readonly PROCESSED_BUCKET = 'processed-documents';
  private readonly TEMP_BUCKET = 'temp-uploads';

  /**
   * Upload a file to Supabase Storage
   */
  async uploadFile(
    file: File,
    path: string,
    bucket: string = this.DOCUMENTS_BUCKET,
    options?: {
      cacheControl?: string;
      contentType?: string;
      upsert?: boolean;
    }
  ): Promise<UploadResult> {
    try {
      const { data, error } = await supabase.storage
        .from(bucket)
        .upload(path, file, {
          cacheControl: options?.cacheControl || '3600',
          contentType: options?.contentType || file.type,
          upsert: options?.upsert || false
        });

      if (error) {
        console.error('Upload error:', error);
        return {
          success: false,
          error: error.message
        };
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from(bucket)
        .getPublicUrl(path);

      return {
        success: true,
        url: urlData.publicUrl,
        path: data.path
      };
    } catch (error) {
      console.error('Upload failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Upload failed'
      };
    }
  }

  /**
   * Upload document for processing
   */
  async uploadDocument(
    file: File,
    userId: string,
    analysisMode: 'basic' | 'advanced' = 'basic'
  ): Promise<UploadResult> {
    const timestamp = Date.now();
    const sanitizedFileName = file.name.replace(/[^a-zA-Z0-9.-]/g, '_');
    const path = `${userId}/${timestamp}_${sanitizedFileName}`;

    const result = await this.uploadFile(file, path, this.DOCUMENTS_BUCKET, {
      contentType: file.type,
      upsert: false
    });

    if (result.success) {
      // Log the upload for processing
      await this.logFileUpload(userId, file, path, analysisMode);
    }

    return result;
  }

  /**
   * Upload processed document result
   */
  async uploadProcessedDocument(
    content: string | Blob,
    originalPath: string,
    userId: string,
    suffix: string = 'processed'
  ): Promise<UploadResult> {
    const pathParts = originalPath.split('/');
    const fileName = pathParts[pathParts.length - 1];
    const nameWithoutExt = fileName.split('.')[0];
    const newPath = `${userId}/${nameWithoutExt}_${suffix}.json`;

    let fileToUpload: File;
    if (typeof content === 'string') {
      fileToUpload = new File([content], `${nameWithoutExt}_${suffix}.json`, {
        type: 'application/json'
      });
    } else {
      fileToUpload = new File([content], `${nameWithoutExt}_${suffix}`, {
        type: content.type || 'application/octet-stream'
      });
    }

    return this.uploadFile(fileToUpload, newPath, this.PROCESSED_BUCKET);
  }

  /**
   * Download a file from storage
   */
  async downloadFile(path: string, bucket: string = this.DOCUMENTS_BUCKET): Promise<Blob | null> {
    try {
      const { data, error } = await supabase.storage
        .from(bucket)
        .download(path);

      if (error) {
        console.error('Download error:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Download failed:', error);
      return null;
    }
  }

  /**
   * Get public URL for a file
   */
  getPublicUrl(path: string, bucket: string = this.DOCUMENTS_BUCKET): string {
    const { data } = supabase.storage
      .from(bucket)
      .getPublicUrl(path);

    return data.publicUrl;
  }

  /**
   * Get signed URL for private file access
   */
  async getSignedUrl(
    path: string,
    expiresIn: number = 3600,
    bucket: string = this.DOCUMENTS_BUCKET
  ): Promise<string | null> {
    try {
      const { data, error } = await supabase.storage
        .from(bucket)
        .createSignedUrl(path, expiresIn);

      if (error) {
        console.error('Signed URL error:', error);
        return null;
      }

      return data.signedUrl;
    } catch (error) {
      console.error('Signed URL failed:', error);
      return null;
    }
  }

  /**
   * Delete a file from storage
   */
  async deleteFile(path: string, bucket: string = this.DOCUMENTS_BUCKET): Promise<boolean> {
    try {
      const { error } = await supabase.storage
        .from(bucket)
        .remove([path]);

      if (error) {
        console.error('Delete error:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Delete failed:', error);
      return false;
    }
  }

  /**
   * List files in a directory
   */
  async listFiles(
    path: string = '',
    bucket: string = this.DOCUMENTS_BUCKET,
    options?: {
      limit?: number;
      offset?: number;
      sortBy?: { column: string; order: 'asc' | 'desc' };
    }
  ): Promise<StorageFile[]> {
    try {
      const { data, error } = await supabase.storage
        .from(bucket)
        .list(path, {
          limit: options?.limit || 100,
          offset: options?.offset || 0,
          sortBy: options?.sortBy || { column: 'name', order: 'asc' }
        });

      if (error) {
        console.error('List files error:', error);
        return [];
      }

      if (!data) return [];
      return data.map((file: any) => ({
        ...file,
        metadata: {
          eTag: file.metadata?.eTag || '',
          size: file.metadata?.size || 0,
          mimetype: file.metadata?.mimetype || '',
          cacheControl: file.metadata?.cacheControl || '',
          lastModified: file.metadata?.lastModified || '',
          contentLength: file.metadata?.contentLength || 0,
          httpStatusCode: file.metadata?.httpStatusCode || 200,
        }
      }));
    } catch (error) {
      console.error('List files failed:', error);
      return [];
    }
  }

  /**
   * Get file metadata
   */
  async getFileMetadata(path: string, bucket: string = this.DOCUMENTS_BUCKET): Promise<any> {
    try {
      const { data, error } = await supabase.storage
        .from(bucket)
        .list('', {
          search: path
        });

      if (error) {
        console.error('Metadata error:', error);
        return null;
      }

      return data?.[0] || null;
    } catch (error) {
      console.error('Metadata failed:', error);
      return null;
    }
  }

  /**
   * Clean up temporary files
   */
  async cleanupTempFiles(olderThanHours: number = 24): Promise<number> {
    try {
      const files = await this.listFiles('', this.TEMP_BUCKET);
      const cutoffTime = Date.now() - (olderThanHours * 60 * 60 * 1000);
      
      let deletedCount = 0;
      
      for (const file of files) {
        const fileTime = new Date(file.created_at).getTime();
        if (fileTime < cutoffTime) {
          const deleted = await this.deleteFile(file.name, this.TEMP_BUCKET);
          if (deleted) deletedCount++;
        }
      }
      
      return deletedCount;
    } catch (error) {
      console.error('Cleanup failed:', error);
      return 0;
    }
  }

  /**
   * Log file upload for processing tracking
   */
  private async logFileUpload(
    userId: string,
    file: File,
    path: string,
    analysisMode: string
  ): Promise<void> {
    try {
      const logData = {
        user_id: userId,
        operation: 'file_upload',
        status: 'completed',
        file_name: file.name,
        file_size: file.size,
        mime_type: file.type,
        metadata: {
          storage_path: path,
          analysis_mode: analysisMode,
          upload_timestamp: new Date().toISOString()
        },
        timestamp: new Date().toISOString(),
        created_at: new Date().toISOString()
      };

      await supabase
        .from('processing_logs')
        .insert([logData]);
    } catch (error) {
      console.error('Failed to log file upload:', error);
    }
  }

  /**
   * Migrate file from old storage system
   */
  async migrateFile(
    oldUrl: string,
    newPath: string,
    bucket: string = this.DOCUMENTS_BUCKET
  ): Promise<UploadResult> {
    try {
      // Download from old URL
      const response = await fetch(oldUrl);
      if (!response.ok) {
        throw new Error(`Failed to fetch file: ${response.statusText}`);
      }

      const blob = await response.blob();
      const file = new File([blob], newPath.split('/').pop() || 'migrated-file', {
        type: blob.type
      });

      // Upload to Supabase Storage
      return this.uploadFile(file, newPath, bucket);
    } catch (error) {
      console.error('Migration failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Migration failed'
      };
    }
  }
}

// Export singleton instance
export const storageService = new SupabaseStorageService();
export default storageService;
