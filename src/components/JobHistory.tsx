import React, { useState, useEffect } from 'react';
import { apiService } from '../services/api';
import { JobData } from '../types';
import { Card, CardBody } from './Card';
import { Button } from './Button';
import { Icon } from './Icon';
import { LoadingSpinner } from './Loading';
import { useToastContext } from './Toast';
import { JobStatus } from './JobStatus';

interface JobHistoryProps {
  userId: string;
  onJobSelect?: (jobId: string) => void;
}

export const JobHistory: React.FC<JobHistoryProps> = ({ userId, onJobSelect }) => {
  const [jobs, setJobs] = useState<JobData[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedJobId, setSelectedJobId] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  const toast = useToastContext();

  const fetchJobs = async () => {
    try {
      setRefreshing(true);
      const response = await apiService.getUserJobsEnhanced(userId, 50);
      if (response.success) {
        setJobs(response.data ?? []);
      }
    } catch (error) {
      console.error('Error fetching job history:', error);
      toast.error('History Load Failed', 'Unable to load job history');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleJobSelect = (jobId: string) => {
    setSelectedJobId(jobId);
    onJobSelect?.(jobId);
  };

  const handleJobComplete = () => {
    fetchJobs(); // Refresh the list
  };

  useEffect(() => {
    fetchJobs();
  }, [userId]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'queued': return 'clock';
      case 'processing': return 'loader';
      case 'completed': return 'check-circle';
      case 'failed': return 'x-circle';
      case 'cancelled': return 'x';
      default: return 'help-circle';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'queued': return 'text-blue-600';
      case 'processing': return 'text-yellow-600';
      case 'completed': return 'text-green-600';
      case 'failed': return 'text-red-600';
      case 'cancelled': return 'text-gray-600';
      default: return 'text-gray-600';
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString();
  };

  if (loading) {
    return (
      <Card className="job-history-card">
        <CardBody>
          <div className="flex items-center justify-center p-8">
            <LoadingSpinner />
            <span className="ml-2">Loading job history...</span>
          </div>
        </CardBody>
      </Card>
    );
  }

  if (jobs.length === 0) {
    return (
      <Card className="job-history-card">
        <CardBody>
          <div className="text-center p-8">
            <Icon name="file-text" size={64} className="text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Processing History</h3>
            <p className="text-gray-600">You haven't uploaded any documents yet.</p>
          </div>
        </CardBody>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900">Processing History</h2>
        <Button
          variant="outline"
          size="small"
          onClick={fetchJobs}
          disabled={refreshing}
          icon={refreshing ? 'loader' : 'refresh-cw'}
        >
          {refreshing ? 'Refreshing...' : 'Refresh'}
        </Button>
      </div>

      {/* Job List */}
      <div className="space-y-3">
        {jobs.map((job) => (
          <Card
            key={job.job_id}
            className={`job-item cursor-pointer transition-all hover:shadow-md ${
              selectedJobId === job.job_id ? 'ring-2 ring-blue-500' : ''
            }`}
            onClick={() => handleJobSelect(job.job_id)}
          >
            <CardBody>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Icon 
                    name={getStatusIcon(job.status)} 
                    size={20} 
                    className={getStatusColor(job.status)}
                  />
                  <div>
                    <h4 className="font-medium text-gray-900 truncate max-w-xs">
                      {job.file_name}
                    </h4>
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <span>{formatFileSize(job.file_size)}</span>
                      <span>{formatDate(job.created_at)}</span>
                      <span>{formatTime(job.created_at)}</span>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(job.status)} bg-opacity-10`}>
                    {job.status}
                  </span>
                  
                  {job.status === 'completed' && job.result && (
                    <span className="text-xs text-gray-500">
                      {job.result.transactionCount} transactions
                    </span>
                  )}
                  
                  <Icon name="chevron-right" size={16} className="text-gray-400" />
                </div>
              </div>

              {/* Progress for active jobs */}
              {(job.status === 'processing' || job.status === 'queued') && job.progress !== undefined && (
                <div className="mt-3">
                  <div className="flex justify-between text-xs text-gray-500 mb-1">
                    <span>Progress</span>
                    <span>{job.progress}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-1">
                    <div 
                      className="bg-blue-600 h-1 rounded-full transition-all duration-300"
                      style={{ width: `${job.progress}%` }}
                    ></div>
                  </div>
                </div>
              )}
            </CardBody>
          </Card>
        ))}
      </div>

      {/* Selected Job Details */}
      {selectedJobId && (
        <div className="mt-6">
          <JobStatus 
            jobId={selectedJobId} 
            onComplete={handleJobComplete}
            onError={handleJobComplete}
          />
        </div>
      )}
    </div>
  );
}; 