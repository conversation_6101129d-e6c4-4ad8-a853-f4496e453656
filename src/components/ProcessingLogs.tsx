import React, { useState, useEffect } from 'react';
import { apiService } from '../services/api';
import { ProcessingLog } from '../types';
import { Card, CardBody } from './Card';
import { Button } from './Button';
import { Icon } from './Icon';
import { LoadingSpinner } from './Loading';
import { useToastContext } from './Toast';

interface ProcessingLogsProps {
  userId?: string;
  limit?: number;
  showUserFilter?: boolean;
}

export const ProcessingLogs: React.FC<ProcessingLogsProps> = ({ 
  userId, 
  limit = 50, 
  showUserFilter = false 
}) => {
  const [logs, setLogs] = useState<ProcessingLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [filterOperation, setFilterOperation] = useState<string>('');
  const [filterStatus, setFilterStatus] = useState<string>('');
  const toast = useToastContext();

  const fetchLogs = async () => {
    try {
      setRefreshing(true);
      const response = await apiService.getProcessingLogs(userId, limit);
      if (response.success) {
        setLogs(response.data ?? []);
      }
    } catch (error) {
      console.error('Error fetching processing logs:', error);
      toast.error('Logs Load Failed', 'Unable to load processing logs');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchLogs();
  }, [userId, limit]);

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed': return 'check-circle';
      case 'failed': return 'x-circle';
      case 'processing': return 'loader';
      case 'pending': return 'clock';
      default: return 'help-circle';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed': return 'text-green-600';
      case 'failed': return 'text-red-600';
      case 'processing': return 'text-yellow-600';
      case 'pending': return 'text-blue-600';
      default: return 'text-gray-600';
    }
  };

  const formatDuration = (durationMs?: number) => {
    if (!durationMs) return 'N/A';
    if (durationMs < 1000) return `${durationMs}ms`;
    return `${(durationMs / 1000).toFixed(1)}s`;
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return 'N/A';
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const filteredLogs = logs.filter(log => {
    const matchesOperation = !filterOperation || log.operation.toLowerCase().includes(filterOperation.toLowerCase());
    const matchesStatus = !filterStatus || log.status.toLowerCase() === filterStatus.toLowerCase();
    return matchesOperation && matchesStatus;
  });

  if (loading) {
    return (
      <Card className="processing-logs-card">
        <CardBody>
          <div className="flex items-center justify-center p-8">
            <LoadingSpinner />
            <span className="ml-2">Loading processing logs...</span>
          </div>
        </CardBody>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header and Filters */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900">Processing Logs</h2>
        <div className="flex items-center space-x-2">
          <select
            value={filterOperation}
            onChange={(e) => setFilterOperation(e.target.value)}
            className="px-3 py-1 border border-gray-300 rounded-md text-sm"
          >
            <option value="">All Operations</option>
            <option value="document_processing">Document Processing</option>
            <option value="text_analysis">Text Analysis</option>
            <option value="image_analysis">Image Analysis</option>
            <option value="data_extraction">Data Extraction</option>
          </select>
          
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="px-3 py-1 border border-gray-300 rounded-md text-sm"
          >
            <option value="">All Status</option>
            <option value="completed">Completed</option>
            <option value="failed">Failed</option>
            <option value="processing">Processing</option>
            <option value="pending">Pending</option>
          </select>
          
          <Button
            variant="outline"
            size="small"
            onClick={fetchLogs}
            disabled={refreshing}
            icon={refreshing ? 'loader' : 'refresh-cw'}
          >
            {refreshing ? 'Refreshing...' : 'Refresh'}
          </Button>
        </div>
      </div>

      {/* Logs List */}
      {filteredLogs.length === 0 ? (
        <Card className="processing-logs-empty">
          <CardBody>
            <div className="text-center p-8">
              <Icon name="file-text" size={64} className="text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Processing Logs</h3>
              <p className="text-gray-600">
                {logs.length === 0 ? 'No processing activities yet.' : 'No logs match the current filters.'}
              </p>
            </div>
          </CardBody>
        </Card>
      ) : (
        <div className="space-y-3">
          {filteredLogs.map((log) => (
            <Card key={log.id} className="processing-log-item">
              <CardBody>
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3">
                    <Icon 
                      name={getStatusIcon(log.status)} 
                      size={20} 
                      className={getStatusColor(log.status)}
                    />
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <h4 className="font-medium text-gray-900">{log.operation}</h4>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(log.status)} bg-opacity-10`}>
                          {log.status}
                        </span>
                      </div>
                      
                      <div className="mt-1 text-sm text-gray-600">
                        {log.file_name && (
                          <div>File: {log.file_name} ({formatFileSize(log.file_size)})</div>
                        )}
                        <div>Duration: {formatDuration(log.duration_ms)}</div>
                        {log.transaction_count && (
                          <div>Transactions: {log.transaction_count}</div>
                        )}
                        {log.confidence_score && (
                          <div>Confidence: {(log.confidence_score * 100).toFixed(1)}%</div>
                        )}
                      </div>
                      
                      {log.error_message && (
                        <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
                          {log.error_message}
                        </div>
                      )}
                      
                      {log.metadata && Object.keys(log.metadata).length > 0 && (
                        <details className="mt-2">
                          <summary className="text-sm text-gray-500 cursor-pointer">Metadata</summary>
                          <pre className="mt-1 text-xs bg-gray-50 p-2 rounded overflow-x-auto">
                            {JSON.stringify(log.metadata, null, 2)}
                          </pre>
                        </details>
                      )}
                    </div>
                  </div>
                  
                  <div className="text-right text-sm text-gray-500">
                    <div>{formatDate(log.timestamp)}</div>
                    {showUserFilter && (
                      <div className="text-xs">User: {log.user_id}</div>
                    )}
                  </div>
                </div>
              </CardBody>
            </Card>
          ))}
        </div>
      )}
      
      {/* Summary */}
      <div className="text-sm text-gray-500 text-center">
        Showing {filteredLogs.length} of {logs.length} processing logs
      </div>
    </div>
  );
};
