import React, { useState, useEffect } from 'react';
import { apiService } from '../services/api';
import { ErrorLog } from '../types';
import { Card, CardBody } from './Card';
import { Button } from './Button';
import { Icon } from './Icon';
import { LoadingSpinner } from './Loading';
import { useToastContext } from './Toast';

interface ErrorLogsProps {
  userId?: string;
  limit?: number;
  showUserFilter?: boolean;
}

export const ErrorLogs: React.FC<ErrorLogsProps> = ({ 
  userId, 
  limit = 50, 
  showUserFilter = false 
}) => {
  const [logs, setLogs] = useState<ErrorLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [filterErrorName, setFilterErrorName] = useState<string>('');
  const [expandedLogs, setExpandedLogs] = useState<Set<string>>(new Set());
  const toast = useToastContext();

  const fetchLogs = async () => {
    try {
      setRefreshing(true);
      const response = await apiService.getErrorLogs(userId, limit);
      if (response.success) {
        setLogs(response.data ?? []);
      }
    } catch (error) {
      console.error('Error fetching error logs:', error);
      toast.error('Error Logs Load Failed', 'Unable to load error logs');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchLogs();
  }, [userId, limit]);

  const toggleExpanded = (logId: string) => {
    const newExpanded = new Set(expandedLogs);
    if (newExpanded.has(logId)) {
      newExpanded.delete(logId);
    } else {
      newExpanded.add(logId);
    }
    setExpandedLogs(newExpanded);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getErrorSeverity = (errorName: string) => {
    const name = errorName.toLowerCase();
    if (name.includes('critical') || name.includes('fatal')) return 'critical';
    if (name.includes('error') || name.includes('exception')) return 'error';
    if (name.includes('warning') || name.includes('warn')) return 'warning';
    return 'info';
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-800 bg-red-100 border-red-300';
      case 'error': return 'text-red-600 bg-red-50 border-red-200';
      case 'warning': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'info': return 'text-blue-600 bg-blue-50 border-blue-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical': return 'alert-triangle';
      case 'error': return 'x-circle';
      case 'warning': return 'alert-circle';
      case 'info': return 'info';
      default: return 'help-circle';
    }
  };

  const filteredLogs = logs.filter(log => {
    const matchesErrorName = !filterErrorName || log.error_name.toLowerCase().includes(filterErrorName.toLowerCase());
    return matchesErrorName;
  });

  if (loading) {
    return (
      <Card className="error-logs-card">
        <CardBody>
          <div className="flex items-center justify-center p-8">
            <LoadingSpinner />
            <span className="ml-2">Loading error logs...</span>
          </div>
        </CardBody>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header and Filters */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900">Error Logs</h2>
        <div className="flex items-center space-x-2">
          <input
            type="text"
            placeholder="Filter by error name..."
            value={filterErrorName}
            onChange={(e) => setFilterErrorName(e.target.value)}
            className="px-3 py-1 border border-gray-300 rounded-md text-sm"
          />
          
          <Button
            variant="outline"
            size="small"
            onClick={fetchLogs}
            disabled={refreshing}
            icon={refreshing ? 'loader' : 'refresh-cw'}
          >
            {refreshing ? 'Refreshing...' : 'Refresh'}
          </Button>
        </div>
      </div>

      {/* Error Summary */}
      {logs.length > 0 && (
        <Card className="error-summary">
          <CardBody>
            <div className="grid grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-red-600">
                  {logs.filter(log => getErrorSeverity(log.error_name) === 'critical').length}
                </div>
                <div className="text-sm text-gray-600">Critical</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-red-500">
                  {logs.filter(log => getErrorSeverity(log.error_name) === 'error').length}
                </div>
                <div className="text-sm text-gray-600">Errors</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-yellow-500">
                  {logs.filter(log => getErrorSeverity(log.error_name) === 'warning').length}
                </div>
                <div className="text-sm text-gray-600">Warnings</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-blue-500">
                  {logs.filter(log => getErrorSeverity(log.error_name) === 'info').length}
                </div>
                <div className="text-sm text-gray-600">Info</div>
              </div>
            </div>
          </CardBody>
        </Card>
      )}

      {/* Error Logs List */}
      {filteredLogs.length === 0 ? (
        <Card className="error-logs-empty">
          <CardBody>
            <div className="text-center p-8">
              <Icon name="check-circle" size={64} className="text-green-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Errors Found</h3>
              <p className="text-gray-600">
                {logs.length === 0 ? 'No errors have been logged.' : 'No errors match the current filter.'}
              </p>
            </div>
          </CardBody>
        </Card>
      ) : (
        <div className="space-y-3">
          {filteredLogs.map((log) => {
            const severity = getErrorSeverity(log.error_name);
            const isExpanded = expandedLogs.has(log.id);
            
            return (
              <Card key={log.id} className={`error-log-item border-l-4 ${getSeverityColor(severity)}`}>
                <CardBody>
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3 flex-1">
                      <Icon 
                        name={getSeverityIcon(severity)} 
                        size={20} 
                        className={getSeverityColor(severity).split(' ')[0]}
                      />
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <h4 className="font-medium text-gray-900">{log.error_name}</h4>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getSeverityColor(severity)}`}>
                            {severity.toUpperCase()}
                          </span>
                        </div>
                        
                        <p className="mt-1 text-sm text-gray-700">{log.error_message}</p>
                        
                        {log.context && Object.keys(log.context).length > 0 && (
                          <div className="mt-2">
                            <div className="text-xs text-gray-500 mb-1">Context:</div>
                            <div className="text-xs bg-gray-50 p-2 rounded">
                              {Object.entries(log.context).map(([key, value]) => (
                                <div key={key}>
                                  <span className="font-medium">{key}:</span> {String(value)}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                        
                        {log.error_stack && (
                          <div className="mt-2">
                            <Button
                              variant="outline"
                              size="small"
                              onClick={() => toggleExpanded(log.id)}
                              icon={isExpanded ? 'chevron-up' : 'chevron-down'}
                            >
                              {isExpanded ? 'Hide' : 'Show'} Stack Trace
                            </Button>
                            
                            {isExpanded && (
                              <pre className="mt-2 text-xs bg-gray-900 text-gray-100 p-3 rounded overflow-x-auto">
                                {log.error_stack}
                              </pre>
                            )}
                          </div>
                        )}
                        
                        <div className="mt-2 text-xs text-gray-500">
                          {log.request_id && <span>Request ID: {log.request_id} • </span>}
                          {formatDate(log.timestamp)}
                        </div>
                      </div>
                    </div>
                    
                    <div className="text-right text-sm text-gray-500">
                      {showUserFilter && log.user_id && (
                        <div className="text-xs">User: {log.user_id}</div>
                      )}
                    </div>
                  </div>
                </CardBody>
              </Card>
            );
          })}
        </div>
      )}
      
      {/* Summary */}
      <div className="text-sm text-gray-500 text-center">
        Showing {filteredLogs.length} of {logs.length} error logs
      </div>
    </div>
  );
};
