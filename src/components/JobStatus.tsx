import React, { useState, useEffect } from 'react';
import { apiService } from '../services/api';
import { JobData } from '../types';
import { Card, CardHeader, CardBody } from './Card';
import { Button } from './Button';
import { Icon } from './Icon';
import { LoadingSpinner } from './Loading';
import { useToastContext } from './Toast';

interface JobStatusProps {
  jobId: string;
  onComplete?: () => void;
  onError?: (error: string) => void;
}

export const JobStatus: React.FC<JobStatusProps> = ({ jobId, onComplete, onError }) => {
  const [jobData, setJobData] = useState<JobData | null>(null);
  const [loading, setLoading] = useState(true);
  const [polling, setPolling] = useState(false);
  const toast = useToastContext();

  const fetchJobStatus = async () => {
    try {
      const response = await apiService.getJobStatusEnhanced(jobId);
      if (response.success) {
        setJobData(response.data ?? null);
        
        // Handle completion
        if (response.data && response.data.status === 'completed') {
          setPolling(false);
          onComplete?.();
          toast.success(
            'Processing Complete!',
            `Successfully processed ${response.data?.result?.transactionCount || 0} transactions.`
          );
        }
        
        // Handle failure
        if (response.data && response.data.status === 'failed') {
          setPolling(false);
          onError?.(response.data?.error || 'Processing failed');
          toast.error(
            'Processing Failed',
            response.data?.error || 'Document processing failed'
          );
        }
        
        // Continue polling if still processing
        if (response.data && (response.data.status === 'queued' || response.data.status === 'processing')) {
          if (!polling) {
            setPolling(true);
          }
        }
      }
    } catch (error) {
      console.error('Error fetching job status:', error);
      toast.error('Status Check Failed', 'Unable to check processing status');
    } finally {
      setLoading(false);
    }
  };

  const cancelJob = async () => {
    try {
      const response = await apiService.cancelJob(jobId);
      if (response.success) {
        setPolling(false);
        toast.success('Job Cancelled', 'Document processing has been cancelled');
        fetchJobStatus(); // Refresh status
      }
    } catch (error) {
      console.error('Error cancelling job:', error);
      toast.error('Cancellation Failed', 'Unable to cancel job');
    }
  };

  useEffect(() => {
    fetchJobStatus();
  }, [jobId]);

  useEffect(() => {
    if (polling) {
      const interval = setInterval(fetchJobStatus, 2000); // Poll every 2 seconds
      return () => clearInterval(interval);
    }
  }, [polling]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'queued': return 'clock';
      case 'processing': return 'loader';
      case 'completed': return 'check-circle';
      case 'failed': return 'x-circle';
      case 'cancelled': return 'x';
      default: return 'help-circle';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'queued': return 'text-blue-600';
      case 'processing': return 'text-yellow-600';
      case 'completed': return 'text-green-600';
      case 'failed': return 'text-red-600';
      case 'cancelled': return 'text-gray-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'queued': return 'Queued for Processing';
      case 'processing': return 'Processing Document';
      case 'completed': return 'Processing Complete';
      case 'failed': return 'Processing Failed';
      case 'cancelled': return 'Processing Cancelled';
      default: return 'Unknown Status';
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  if (loading) {
    return (
      <Card className="job-status-card">
        <CardBody>
          <div className="flex items-center justify-center p-4">
            <LoadingSpinner />
            <span className="ml-2">Loading job status...</span>
          </div>
        </CardBody>
      </Card>
    );
  }

  if (!jobData) {
    return (
      <Card className="job-status-card">
        <CardBody>
          <div className="text-center p-4">
            <Icon name="alert-circle" size={48} className="text-red-500 mx-auto mb-2" />
            <p className="text-gray-600">Job not found</p>
          </div>
        </CardBody>
      </Card>
    );
  }

  return (
    <Card className="job-status-card">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Icon 
              name={getStatusIcon(jobData.status)} 
              size={24} 
              className={`mr-2 ${getStatusColor(jobData.status)}`}
            />
            <h3 className="card-header">{jobData.file_name}</h3>
          </div>
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(jobData.status)} bg-opacity-10`}>
            {getStatusText(jobData.status)}
          </span>
        </div>
      </CardHeader>
      
      <CardBody>
        <div className="space-y-4">
          {/* Progress Bar */}
          {(jobData.status === 'processing' || jobData.status === 'queued') && (
            <div className="w-full">
              <div className="flex justify-between text-sm text-gray-600 mb-1">
                <span>Progress</span>
                <span>{jobData.progress || 0}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${jobData.progress || 0}%` }}
                ></div>
              </div>
            </div>
          )}

          {/* File Information */}
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-500">File Size:</span>
              <span className="ml-2 font-medium">{formatFileSize(jobData.file_size)}</span>
            </div>
            <div>
              <span className="text-gray-500">Type:</span>
              <span className="ml-2 font-medium">{jobData.mime_type}</span>
            </div>
            <div>
              <span className="text-gray-500">Created:</span>
              <span className="ml-2 font-medium">{formatDate(jobData.created_at)}</span>
            </div>
            {jobData.started_at && (
              <div>
                <span className="text-gray-500">Started:</span>
                <span className="ml-2 font-medium">{formatDate(jobData.started_at)}</span>
              </div>
            )}
            {jobData.completed_at && (
              <div>
                <span className="text-gray-500">Completed:</span>
                <span className="ml-2 font-medium">{formatDate(jobData.completed_at)}</span>
              </div>
            )}
          </div>

          {/* Results */}
          {jobData.status === 'completed' && jobData.result && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-3">
              <h4 className="font-medium text-green-800 mb-2">Processing Results</h4>
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="text-green-600">Transactions:</span>
                  <span className="ml-2 font-medium">{jobData.result.transactionCount}</span>
                </div>
                <div>
                  <span className="text-green-600">Time:</span>
                  <span className="ml-2 font-medium">{jobData.result.processingTime}ms</span>
                </div>
                <div>
                  <span className="text-green-600">Confidence:</span>
                  <span className="ml-2 font-medium">{(jobData.result.confidence * 100).toFixed(1)}%</span>
                </div>
              </div>
            </div>
          )}

          {/* Error Information */}
          {jobData.status === 'failed' && jobData.error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <h4 className="font-medium text-red-800 mb-2">Error Details</h4>
              <p className="text-red-700 text-sm">{jobData.error}</p>
            </div>
          )}

          {/* Actions */}
          <div className="flex justify-end space-x-2">
            {(jobData.status === 'queued' || jobData.status === 'processing') && (
              <Button
                variant="outline"
                size="small"
                onClick={cancelJob}
                icon="x"
              >
                Cancel
              </Button>
            )}
            
            {jobData.status === 'completed' && (
              <Button
                variant="primary"
                size="small"
                onClick={onComplete}
                icon="check"
              >
                View Results
              </Button>
            )}
          </div>
        </div>
      </CardBody>
    </Card>
  );
}; 