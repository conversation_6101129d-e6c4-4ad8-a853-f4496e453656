-- FinScope Supabase Migration: Create tables for document processing and logging
-- This file creates the necessary tables to replace Firebase Firestore collections

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Processing logs table (replaces Firebase 'processing_logs' collection)
CREATE TABLE IF NOT EXISTS processing_logs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id TEXT NOT NULL,
    operation TEXT NOT NULL,
    status TEXT NOT NULL,
    duration_ms INTEGER,
    file_name TEXT,
    file_size INTEGER,
    mime_type TEXT,
    transaction_count INTEGER,
    confidence_score DECIMAL(3,2),
    error_message TEXT,
    metadata JSONB,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Audit trail table (replaces Firebase 'audit_trail' collection)
CREATE TABLE IF NOT EXISTS audit_trail (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id TEXT NOT NULL,
    action TEXT NOT NULL,
    resource_type TEXT NOT NULL,
    resource_id TEXT,
    old_values JSONB,
    new_values JSONB,
    ip_address TEXT,
    user_agent TEXT,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Batch metadata table (replaces Firebase 'batch_metadata' collection)
CREATE TABLE IF NOT EXISTS batch_metadata (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    batch_id TEXT UNIQUE NOT NULL,
    user_id TEXT NOT NULL,
    status TEXT NOT NULL CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),
    file_count INTEGER DEFAULT 0,
    processed_count INTEGER DEFAULT 0,
    failed_count INTEGER DEFAULT 0,
    total_transactions INTEGER DEFAULT 0,
    processing_time_ms INTEGER,
    confidence_score DECIMAL(3,2),
    error_message TEXT,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Job status table (replaces Firebase 'job_status' collection)
CREATE TABLE IF NOT EXISTS job_status (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    job_id TEXT UNIQUE NOT NULL,
    user_id TEXT NOT NULL,
    file_name TEXT NOT NULL,
    status TEXT NOT NULL CHECK (status IN ('queued', 'processing', 'completed', 'failed', 'cancelled')),
    file_size INTEGER NOT NULL,
    mime_type TEXT NOT NULL,
    progress INTEGER DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
    error TEXT,
    result JSONB,
    options JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    failed_at TIMESTAMP WITH TIME ZONE,
    cancelled_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Error logs table (replaces Firebase 'error_logs' collection)
CREATE TABLE IF NOT EXISTS error_logs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    error_name TEXT NOT NULL,
    error_message TEXT NOT NULL,
    error_stack TEXT,
    context JSONB,
    user_id TEXT,
    request_id TEXT,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Performance logs table (replaces Firebase 'performance_logs' collection)
CREATE TABLE IF NOT EXISTS performance_logs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    operation TEXT NOT NULL,
    duration INTEGER NOT NULL, -- in milliseconds
    metadata JSONB,
    user_id TEXT,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_processing_logs_user_id ON processing_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_processing_logs_timestamp ON processing_logs(timestamp);
CREATE INDEX IF NOT EXISTS idx_audit_trail_user_id ON audit_trail(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_trail_timestamp ON audit_trail(timestamp);
CREATE INDEX IF NOT EXISTS idx_batch_metadata_user_id ON batch_metadata(user_id);
CREATE INDEX IF NOT EXISTS idx_batch_metadata_status ON batch_metadata(status);
CREATE INDEX IF NOT EXISTS idx_job_status_user_id ON job_status(user_id);
CREATE INDEX IF NOT EXISTS idx_job_status_status ON job_status(status);
CREATE INDEX IF NOT EXISTS idx_job_status_created_at ON job_status(created_at);
CREATE INDEX IF NOT EXISTS idx_error_logs_timestamp ON error_logs(timestamp);
CREATE INDEX IF NOT EXISTS idx_performance_logs_operation ON performance_logs(operation);
CREATE INDEX IF NOT EXISTS idx_performance_logs_timestamp ON performance_logs(timestamp);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at columns
CREATE TRIGGER update_batch_metadata_updated_at BEFORE UPDATE ON batch_metadata FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_job_status_updated_at BEFORE UPDATE ON job_status FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) policies
ALTER TABLE processing_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_trail ENABLE ROW LEVEL SECURITY;
ALTER TABLE batch_metadata ENABLE ROW LEVEL SECURITY;
ALTER TABLE job_status ENABLE ROW LEVEL SECURITY;
ALTER TABLE error_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE performance_logs ENABLE ROW LEVEL SECURITY;

-- RLS policies for processing_logs
CREATE POLICY "Users can view their own processing logs" ON processing_logs
    FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY "Service role can manage all processing logs" ON processing_logs
    FOR ALL USING (auth.role() = 'service_role');

-- RLS policies for audit_trail
CREATE POLICY "Users can view their own audit trail" ON audit_trail
    FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY "Service role can manage all audit trail" ON audit_trail
    FOR ALL USING (auth.role() = 'service_role');

-- RLS policies for batch_metadata
CREATE POLICY "Users can view their own batch metadata" ON batch_metadata
    FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY "Service role can manage all batch metadata" ON batch_metadata
    FOR ALL USING (auth.role() = 'service_role');

-- RLS policies for job_status
CREATE POLICY "Users can view their own job status" ON job_status
    FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY "Service role can manage all job status" ON job_status
    FOR ALL USING (auth.role() = 'service_role');

-- RLS policies for error_logs
CREATE POLICY "Service role can manage all error logs" ON error_logs
    FOR ALL USING (auth.role() = 'service_role');

-- RLS policies for performance_logs
CREATE POLICY "Service role can manage all performance logs" ON performance_logs
    FOR ALL USING (auth.role() = 'service_role');

-- Create storage bucket for documents (if not exists)
INSERT INTO storage.buckets (id, name, public)
VALUES ('documents', 'documents', true)
ON CONFLICT (id) DO NOTHING;

-- Storage policies for documents bucket
CREATE POLICY "Users can upload their own documents" ON storage.objects
    FOR INSERT WITH CHECK (bucket_id = 'documents' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can view their own documents" ON storage.objects
    FOR SELECT USING (bucket_id = 'documents' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can update their own documents" ON storage.objects
    FOR UPDATE USING (bucket_id = 'documents' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can delete their own documents" ON storage.objects
    FOR DELETE USING (bucket_id = 'documents' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Service role can manage all documents" ON storage.objects
    FOR ALL USING (bucket_id = 'documents' AND auth.role() = 'service_role');

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO service_role;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO service_role;

-- Comments for documentation
COMMENT ON TABLE processing_logs IS 'Logs for document processing operations, replacing Firebase processing_logs collection';
COMMENT ON TABLE audit_trail IS 'Audit trail for user actions, replacing Firebase audit_trail collection';
COMMENT ON TABLE batch_metadata IS 'Metadata for batch processing operations, replacing Firebase batch_metadata collection';
COMMENT ON TABLE job_status IS 'Status tracking for processing jobs, replacing Firebase job_status collection';
COMMENT ON TABLE error_logs IS 'Error logging for the application, replacing Firebase error_logs collection';
COMMENT ON TABLE performance_logs IS 'Performance monitoring logs, replacing Firebase performance_logs collection';
