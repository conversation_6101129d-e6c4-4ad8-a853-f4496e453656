-- ===================================================================
-- COMPREHENSIVE FINScope SUPABASE SCHEMA (WITH EXISTING POLICY HANDLING)
-- Copy and paste this entire script into your Supabase SQL Editor
-- This handles existing policies gracefully
-- ===================================================================
-- Enable UUID extension
create extension IF not exists "uuid-ossp";

-- ===================================================================
-- 1. CORE TRANSACTIONS TABLE
-- ===================================================================
create table if not exists transactions (
  id UUID default gen_random_uuid () primary key,
  user_id TEXT not null,
  fingerprint TEXT,
  date DATE not null,
  time TIME,
  description TEXT not null,
  amount DECIMAL(15, 2) not null,
  type TEXT not null check (type in ('credit', 'debit')),
  category TEXT,
  vendor TEXT,
  bank_provided_id TEXT,
  source_bank TEXT not null,
  created_at timestamp with time zone default NOW(),
  updated_at timestamp with time zone default NOW(),
  -- Processing metadata fields
  confidence_score DECIMAL(3, 2) default 1.0,
  processing_metadata JSONB,
  batch_id UUID,
  processing_id UUID,
  -- Enhanced optional fields
  sender_name TEXT,
  receiver_name TEXT,
  sender_account_number TEXT,
  receiver_account_number TEXT,
  account_type TEXT,
  customer_id TEXT,
  transaction_fee DECIMAL(15, 2),
  currency_type TEXT default 'NGN',
  exchange_rate DECIMAL(10, 4),
  transaction_status TEXT default 'Successful',
  payment_method TEXT,
  narration TEXT,
  invoice_number TEXT,
  merchant_name TEXT,
  authorization_code TEXT,
  destination_bank TEXT,
  bank_branch TEXT,
  payment_gateway TEXT,
  terminal_id TEXT,
  channel TEXT,
  session_id TEXT,
  device_id TEXT,
  ip_address TEXT,
  geolocation TEXT,
  balance_before DECIMAL(15, 2),
  balance_after DECIMAL(15, 2)
);

-- ===================================================================
-- 2. CATEGORIES TABLE (for transaction categorization)
-- ===================================================================
create table if not exists categories (
  id UUID default gen_random_uuid () primary key,
  user_id TEXT not null,
  name TEXT not null,
  color TEXT default '#3B82F6',
  icon TEXT,
  created_at timestamp with time zone default NOW(),
  updated_at timestamp with time zone default NOW(),
  unique (user_id, name)
);

-- ===================================================================
-- 3. VENDORS TABLE (for vendor management)
-- ===================================================================
create table if not exists vendors (
  id UUID default gen_random_uuid () primary key,
  user_id TEXT not null,
  name TEXT not null,
  normalized_name TEXT not null,
  category_id UUID references categories (id) on delete set null,
  created_at timestamp with time zone default NOW(),
  updated_at timestamp with time zone default NOW(),
  unique (user_id, normalized_name)
);

-- ===================================================================
-- 4. PROCESSING LOGS TABLE
-- ===================================================================
create table if not exists processing_logs (
  id UUID default gen_random_uuid () primary key,
  user_id TEXT not null,
  operation TEXT not null,
  status TEXT not null,
  duration_ms INTEGER,
  file_name TEXT,
  file_size INTEGER,
  mime_type TEXT,
  transaction_count INTEGER,
  confidence_score DECIMAL(3, 2),
  error_message TEXT,
  metadata JSONB,
  timestamp timestamp with time zone default NOW(),
  created_at timestamp with time zone default NOW()
);

-- ===================================================================
-- 5. AUDIT TRAIL TABLE
-- ===================================================================
create table if not exists audit_trail (
  id UUID default gen_random_uuid () primary key,
  user_id TEXT not null,
  action TEXT not null,
  resource_type TEXT not null,
  resource_id TEXT,
  old_values JSONB,
  new_values JSONB,
  ip_address TEXT,
  user_agent TEXT,
  timestamp timestamp with time zone default NOW(),
  created_at timestamp with time zone default NOW()
);

-- ===================================================================
-- 6. BATCH METADATA TABLE
-- ===================================================================
create table if not exists batch_metadata (
  id UUID default gen_random_uuid () primary key,
  batch_id TEXT unique not null,
  user_id TEXT not null,
  status TEXT not null check (
    status in (
      'pending',
      'processing',
      'completed',
      'failed',
      'cancelled'
    )
  ),
  file_count INTEGER default 0,
  processed_count INTEGER default 0,
  failed_count INTEGER default 0,
  total_transactions INTEGER default 0,
  processing_time_ms INTEGER,
  confidence_score DECIMAL(3, 2),
  error_message TEXT,
  metadata JSONB,
  created_at timestamp with time zone default NOW(),
  updated_at timestamp with time zone default NOW()
);

-- ===================================================================
-- 7. JOB STATUS TABLE
-- ===================================================================
create table if not exists job_status (
  id UUID default gen_random_uuid () primary key,
  job_id TEXT unique not null,
  user_id TEXT not null,
  file_name TEXT not null,
  status TEXT not null check (
    status in (
      'queued',
      'processing',
      'completed',
      'failed',
      'cancelled'
    )
  ),
  file_size INTEGER not null,
  mime_type TEXT not null,
  progress INTEGER default 0 check (
    progress >= 0
    and progress <= 100
  ),
  error TEXT,
  result JSONB,
  options JSONB,
  created_at timestamp with time zone default NOW(),
  started_at timestamp with time zone,
  completed_at timestamp with time zone,
  failed_at timestamp with time zone,
  cancelled_at timestamp with time zone,
  updated_at timestamp with time zone default NOW()
);

-- ===================================================================
-- 8. ERROR LOGS TABLE
-- ===================================================================
create table if not exists error_logs (
  id UUID default gen_random_uuid () primary key,
  error_name TEXT not null,
  error_message TEXT not null,
  error_stack TEXT,
  context JSONB,
  user_id TEXT,
  request_id TEXT,
  timestamp timestamp with time zone default NOW(),
  created_at timestamp with time zone default NOW()
);

-- ===================================================================
-- 9. PERFORMANCE LOGS TABLE
-- ===================================================================
create table if not exists performance_logs (
  id UUID default gen_random_uuid () primary key,
  operation TEXT not null,
  duration INTEGER not null, -- in milliseconds
  metadata JSONB,
  user_id TEXT,
  timestamp timestamp with time zone default NOW(),
  created_at timestamp with time zone default NOW()
);

-- ===================================================================
-- 10. DOCUMENTS TABLE (for document storage metadata)
-- ===================================================================
create table if not exists documents (
  id UUID default gen_random_uuid () primary key,
  user_id TEXT not null,
  file_name TEXT not null,
  file_path TEXT not null,
  file_size INTEGER not null,
  mime_type TEXT not null,
  status TEXT default 'uploaded' check (
    status in ('uploaded', 'processing', 'processed', 'failed')
  ),
  processing_result JSONB,
  metadata JSONB,
  created_at timestamp with time zone default NOW(),
  updated_at timestamp with time zone default NOW()
);

-- ===================================================================
-- 11. DOCUMENT PROCESSING LOGS TABLE
-- ===================================================================
create table if not exists document_processing_logs (
  id UUID default gen_random_uuid () primary key,
  user_id TEXT not null,
  document_id UUID references documents (id) on delete CASCADE,
  operation TEXT not null,
  status TEXT not null,
  duration_ms INTEGER,
  transaction_count INTEGER default 0,
  confidence_score DECIMAL(3, 2),
  error_message TEXT,
  metadata JSONB,
  created_at timestamp with time zone default NOW()
);

-- ===================================================================
-- 12. PROCESSING HISTORY TABLE
-- ===================================================================
create table if not exists processing_history (
  id UUID default gen_random_uuid () primary key,
  user_id TEXT not null,
  document_type TEXT not null,
  source TEXT,
  file_name TEXT,
  file_size INTEGER,
  mime_type TEXT,
  processing_time_ms INTEGER,
  confidence_score DECIMAL(3, 2),
  transaction_count INTEGER default 0,
  success BOOLEAN default true,
  error_message TEXT,
  raw_text TEXT,
  metadata JSONB,
  created_at timestamp with time zone default NOW()
);

-- ===================================================================
-- INDEXES FOR OPTIMAL PERFORMANCE
-- ===================================================================
-- Transactions indexes
create index IF not exists idx_transactions_user_id on transactions (user_id);

create index IF not exists idx_transactions_date on transactions (date);

create index IF not exists idx_transactions_category on transactions (category);

create index IF not exists idx_transactions_type on transactions (type);

create index IF not exists idx_transactions_fingerprint on transactions (fingerprint);

create index IF not exists idx_transactions_user_date on transactions (user_id, date desc);

create index IF not exists idx_transactions_user_category on transactions (user_id, category);

create index IF not exists idx_transactions_user_type on transactions (user_id, type);

create index IF not exists idx_transactions_user_amount on transactions (user_id, amount desc);

create index IF not exists idx_transactions_date_amount on transactions (date, amount)
where
  amount > 0;

create index IF not exists idx_transactions_batch_processing on transactions (batch_id, processing_id);

create index IF not exists idx_transactions_vendor on transactions (user_id, vendor)
where
  vendor is not null;

create index IF not exists idx_transactions_source_bank on transactions (user_id, source_bank);

create index IF not exists idx_transactions_created_at on transactions (created_at desc);

create index IF not exists idx_transactions_date_type on transactions (date, type);

create index IF not exists idx_transactions_user_date_category on transactions (user_id, date, category);

create index IF not exists idx_transactions_user_type_amount on transactions (user_id, type, amount desc);

create index IF not exists idx_transactions_merchant_name on transactions (merchant_name);

create index IF not exists idx_transactions_payment_method on transactions (payment_method);

create index IF not exists idx_transactions_transaction_status on transactions (transaction_status);

create index IF not exists idx_transactions_currency_type on transactions (currency_type);

create index IF not exists idx_transactions_payment_gateway on transactions (payment_gateway);

create index IF not exists idx_transactions_channel on transactions (channel);

-- Categories indexes
create index IF not exists idx_categories_user_id on categories (user_id);

create index IF not exists idx_categories_name on categories (name);

-- Vendors indexes
create index IF not exists idx_vendors_user_id on vendors (user_id);

create index IF not exists idx_vendors_normalized_name on vendors (normalized_name);

create index IF not exists idx_vendors_category_id on vendors (category_id);

-- Processing logs indexes
create index IF not exists idx_processing_logs_user_id on processing_logs (user_id);

create index IF not exists idx_processing_logs_timestamp on processing_logs (timestamp);

create index IF not exists idx_processing_logs_operation on processing_logs (operation);

-- Audit trail indexes
create index IF not exists idx_audit_trail_user_id on audit_trail (user_id);

create index IF not exists idx_audit_trail_timestamp on audit_trail (timestamp);

create index IF not exists idx_audit_trail_action on audit_trail (action);

-- Batch metadata indexes
create index IF not exists idx_batch_metadata_user_id on batch_metadata (user_id);

create index IF not exists idx_batch_metadata_status on batch_metadata (status);

create index IF not exists idx_batch_metadata_batch_id on batch_metadata (batch_id);

-- Job status indexes
create index IF not exists idx_job_status_user_id on job_status (user_id);

create index IF not exists idx_job_status_status on job_status (status);

create index IF not exists idx_job_status_created_at on job_status (created_at);

create index IF not exists idx_job_status_job_id on job_status (job_id);

-- Error logs indexes
create index IF not exists idx_error_logs_timestamp on error_logs (timestamp);

create index IF not exists idx_error_logs_user_id on error_logs (user_id);

create index IF not exists idx_error_logs_error_name on error_logs (error_name);

-- Performance logs indexes
create index IF not exists idx_performance_logs_operation on performance_logs (operation);

create index IF not exists idx_performance_logs_timestamp on performance_logs (timestamp);

create index IF not exists idx_performance_logs_user_id on performance_logs (user_id);

-- Documents indexes
create index IF not exists idx_documents_user_id on documents (user_id);

create index IF not exists idx_documents_status on documents (status);

create index IF not exists idx_documents_created_at on documents (created_at);

-- Document processing logs indexes
create index IF not exists idx_document_processing_logs_user_id on document_processing_logs (user_id);

create index IF not exists idx_document_processing_logs_document_id on document_processing_logs (document_id);

create index IF not exists idx_document_processing_logs_created_at on document_processing_logs (created_at);

-- Processing history indexes
create index IF not exists idx_processing_history_user_id on processing_history (user_id);

create index IF not exists idx_processing_history_created_at on processing_history (created_at);

create index IF not exists idx_processing_history_document_type on processing_history (document_type);

-- ===================================================================
-- CONSTRAINTS
-- ===================================================================
-- Transactions constraints
do $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'check_amount_positive') THEN
        ALTER TABLE transactions ADD CONSTRAINT check_amount_positive CHECK (amount > 0);
    END IF;
END$$;

do $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'unique_user_fingerprint') THEN
        ALTER TABLE transactions ADD CONSTRAINT unique_user_fingerprint UNIQUE (user_id, fingerprint);
    END IF;
END$$;

-- ===================================================================
-- TRIGGERS
-- ===================================================================
-- Create or replace function for updated_at trigger
create or replace function update_updated_at_column () RETURNS TRIGGER LANGUAGE plpgsql SECURITY DEFINER
set
  search_path = public as $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;

-- Create triggers for updated_at columns
do $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_transactions_updated_at') THEN
        CREATE TRIGGER update_transactions_updated_at 
            BEFORE UPDATE ON transactions 
            FOR EACH ROW 
            EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

do $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_categories_updated_at') THEN
        CREATE TRIGGER update_categories_updated_at 
            BEFORE UPDATE ON categories 
            FOR EACH ROW 
            EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

do $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_vendors_updated_at') THEN
        CREATE TRIGGER update_vendors_updated_at 
            BEFORE UPDATE ON vendors 
            FOR EACH ROW 
            EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

do $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_batch_metadata_updated_at') THEN
        CREATE TRIGGER update_batch_metadata_updated_at 
            BEFORE UPDATE ON batch_metadata 
            FOR EACH ROW 
            EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

do $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_job_status_updated_at') THEN
        CREATE TRIGGER update_job_status_updated_at 
            BEFORE UPDATE ON job_status 
            FOR EACH ROW 
            EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

do $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_documents_updated_at') THEN
        CREATE TRIGGER update_documents_updated_at 
            BEFORE UPDATE ON documents 
            FOR EACH ROW 
            EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- ===================================================================
-- ROW LEVEL SECURITY (RLS) - WITH EXISTING POLICY HANDLING
-- ===================================================================
-- Enable RLS on all tables
alter table transactions ENABLE row LEVEL SECURITY;

alter table categories ENABLE row LEVEL SECURITY;

alter table vendors ENABLE row LEVEL SECURITY;

alter table processing_logs ENABLE row LEVEL SECURITY;

alter table audit_trail ENABLE row LEVEL SECURITY;

alter table batch_metadata ENABLE row LEVEL SECURITY;

alter table job_status ENABLE row LEVEL SECURITY;

alter table error_logs ENABLE row LEVEL SECURITY;

alter table performance_logs ENABLE row LEVEL SECURITY;

alter table documents ENABLE row LEVEL SECURITY;

alter table document_processing_logs ENABLE row LEVEL SECURITY;

alter table processing_history ENABLE row LEVEL SECURITY;

-- RLS Policies for transactions
do $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'transactions' AND policyname = 'Users can view own transactions') THEN
        CREATE POLICY "Users can view own transactions" ON transactions
            FOR SELECT USING (auth.uid()::text = user_id);
    END IF;
END $$;

do $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'transactions' AND policyname = 'Users can insert own transactions') THEN
        CREATE POLICY "Users can insert own transactions" ON transactions
            FOR INSERT WITH CHECK (auth.uid()::text = user_id);
    END IF;
END $$;

do $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'transactions' AND policyname = 'Users can update own transactions') THEN
        CREATE POLICY "Users can update own transactions" ON transactions
            FOR UPDATE USING (auth.uid()::text = user_id);
    END IF;
END $$;

do $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'transactions' AND policyname = 'Users can delete own transactions') THEN
        CREATE POLICY "Users can delete own transactions" ON transactions
            FOR DELETE USING (auth.uid()::text = user_id);
    END IF;
END $$;

-- RLS Policies for categories
do $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'categories' AND policyname = 'Users can view own categories') THEN
        CREATE POLICY "Users can view own categories" ON categories
            FOR SELECT USING (auth.uid()::text = user_id);
    END IF;
END $$;

do $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'categories' AND policyname = 'Users can insert own categories') THEN
        CREATE POLICY "Users can insert own categories" ON categories
            FOR INSERT WITH CHECK (auth.uid()::text = user_id);
    END IF;
END $$;

do $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'categories' AND policyname = 'Users can update own categories') THEN
        CREATE POLICY "Users can update own categories" ON categories
            FOR UPDATE USING (auth.uid()::text = user_id);
    END IF;
END $$;

do $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'categories' AND policyname = 'Users can delete own categories') THEN
        CREATE POLICY "Users can delete own categories" ON categories
            FOR DELETE USING (auth.uid()::text = user_id);
    END IF;
END $$;

-- RLS Policies for vendors
do $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'vendors' AND policyname = 'Users can view own vendors') THEN
        CREATE POLICY "Users can view own vendors" ON vendors
            FOR SELECT USING (auth.uid()::text = user_id);
    END IF;
END $$;

do $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'vendors' AND policyname = 'Users can insert own vendors') THEN
        CREATE POLICY "Users can insert own vendors" ON vendors
            FOR INSERT WITH CHECK (auth.uid()::text = user_id);
    END IF;
END $$;

do $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'vendors' AND policyname = 'Users can update own vendors') THEN
        CREATE POLICY "Users can update own vendors" ON vendors
            FOR UPDATE USING (auth.uid()::text = user_id);
    END IF;
END $$;

do $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'vendors' AND policyname = 'Users can delete own vendors') THEN
        CREATE POLICY "Users can delete own vendors" ON vendors
            FOR DELETE USING (auth.uid()::text = user_id);
    END IF;
END $$;

-- RLS Policies for processing_logs
do $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'processing_logs' AND policyname = 'Users can view their own processing logs') THEN
        CREATE POLICY "Users can view their own processing logs" ON processing_logs
            FOR SELECT USING (auth.uid()::text = user_id);
    END IF;
END $$;

do $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'processing_logs' AND policyname = 'Service role can manage all processing logs') THEN
        CREATE POLICY "Service role can manage all processing logs" ON processing_logs
            FOR ALL USING (auth.role() = 'service_role');
    END IF;
END $$;

-- RLS Policies for audit_trail
do $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'audit_trail' AND policyname = 'Users can view their own audit trail') THEN
        CREATE POLICY "Users can view their own audit trail" ON audit_trail
            FOR SELECT USING (auth.uid()::text = user_id);
    END IF;
END $$;

do $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'audit_trail' AND policyname = 'Service role can manage all audit trail') THEN
        CREATE POLICY "Service role can manage all audit trail" ON audit_trail
            FOR ALL USING (auth.role() = 'service_role');
    END IF;
END $$;

-- RLS Policies for batch_metadata
do $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'batch_metadata' AND policyname = 'Users can view their own batch metadata') THEN
        CREATE POLICY "Users can view their own batch metadata" ON batch_metadata
            FOR SELECT USING (auth.uid()::text = user_id);
    END IF;
END $$;

do $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'batch_metadata' AND policyname = 'Service role can manage all batch metadata') THEN
        CREATE POLICY "Service role can manage all batch metadata" ON batch_metadata
            FOR ALL USING (auth.role() = 'service_role');
    END IF;
END $$;

-- RLS Policies for job_status
do $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'job_status' AND policyname = 'Users can view their own job status') THEN
        CREATE POLICY "Users can view their own job status" ON job_status
            FOR SELECT USING (auth.uid()::text = user_id);
    END IF;
END $$;

do $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'job_status' AND policyname = 'Service role can manage all job status') THEN
        CREATE POLICY "Service role can manage all job status" ON job_status
            FOR ALL USING (auth.role() = 'service_role');
    END IF;
END $$;

-- RLS Policies for error_logs
do $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'error_logs' AND policyname = 'Service role can manage all error logs') THEN
        CREATE POLICY "Service role can manage all error logs" ON error_logs
            FOR ALL USING (auth.role() = 'service_role');
    END IF;
END $$;

-- RLS Policies for performance_logs
do $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'performance_logs' AND policyname = 'Service role can manage all performance logs') THEN
        CREATE POLICY "Service role can manage all performance logs" ON performance_logs
            FOR ALL USING (auth.role() = 'service_role');
    END IF;
END $$;

-- RLS Policies for documents
do $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'documents' AND policyname = 'Users can view their own documents') THEN
        CREATE POLICY "Users can view their own documents" ON documents
            FOR SELECT USING (auth.uid()::text = user_id);
    END IF;
END $$;

do $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'documents' AND policyname = 'Users can insert their own documents') THEN
        CREATE POLICY "Users can insert their own documents" ON documents
            FOR INSERT WITH CHECK (auth.uid()::text = user_id);
    END IF;
END $$;

do $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'documents' AND policyname = 'Users can update their own documents') THEN
        CREATE POLICY "Users can update their own documents" ON documents
            FOR UPDATE USING (auth.uid()::text = user_id);
    END IF;
END $$;

do $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'documents' AND policyname = 'Users can delete their own documents') THEN
        CREATE POLICY "Users can delete their own documents" ON documents
            FOR DELETE USING (auth.uid()::text = user_id);
    END IF;
END $$;

-- RLS Policies for document_processing_logs
do $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'document_processing_logs' AND policyname = 'Users can view their own document processing logs') THEN
        CREATE POLICY "Users can view their own document processing logs" ON document_processing_logs
            FOR SELECT USING (auth.uid()::text = user_id);
    END IF;
END $$;

do $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'document_processing_logs' AND policyname = 'Service role can manage all document processing logs') THEN
        CREATE POLICY "Service role can manage all document processing logs" ON document_processing_logs
            FOR ALL USING (auth.role() = 'service_role');
    END IF;
END $$;

-- RLS Policies for processing_history
do $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'processing_history' AND policyname = 'Users can view their own processing history') THEN
        CREATE POLICY "Users can view their own processing history" ON processing_history
            FOR SELECT USING (auth.uid()::text = user_id);
    END IF;
END $$;

do $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'processing_history' AND policyname = 'Users can insert their own processing history') THEN
        CREATE POLICY "Users can insert their own processing history" ON processing_history
            FOR INSERT WITH CHECK (auth.uid()::text = user_id);
    END IF;
END $$;

-- ===================================================================
-- STORAGE BUCKETS
-- ===================================================================
-- Create storage bucket for documents (if not exists)
insert into
  storage.buckets (id, name, public)
values
  ('documents', 'documents', true)
on conflict (id) do nothing;

insert into
  storage.buckets (id, name, public)
values
  (
    'processed-documents',
    'processed-documents',
    true
  )
on conflict (id) do nothing;

-- Storage policies for documents bucket
do $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'objects' AND policyname = 'Users can upload their own documents') THEN
        CREATE POLICY "Users can upload their own documents" ON storage.objects
            FOR INSERT WITH CHECK (bucket_id = 'documents' AND auth.uid()::text = (storage.foldername(name))[1]);
    END IF;
END $$;

do $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'objects' AND policyname = 'Users can view their own documents') THEN
        CREATE POLICY "Users can view their own documents" ON storage.objects
            FOR SELECT USING (bucket_id = 'documents' AND auth.uid()::text = (storage.foldername(name))[1]);
    END IF;
END $$;

do $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'objects' AND policyname = 'Users can update their own documents') THEN
        CREATE POLICY "Users can update their own documents" ON storage.objects
            FOR UPDATE USING (bucket_id = 'documents' AND auth.uid()::text = (storage.foldername(name))[1]);
    END IF;
END $$;

do $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'objects' AND policyname = 'Users can delete their own documents') THEN
        CREATE POLICY "Users can delete their own documents" ON storage.objects
            FOR DELETE USING (bucket_id = 'documents' AND auth.uid()::text = (storage.foldername(name))[1]);
    END IF;
END $$;

do $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'objects' AND policyname = 'Service role can manage all documents') THEN
        CREATE POLICY "Service role can manage all documents" ON storage.objects
            FOR ALL USING (bucket_id = 'documents' AND auth.role() = 'service_role');
    END IF;
END $$;

-- Storage policies for processed-documents bucket
do $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'objects' AND policyname = 'Users can upload their own processed documents') THEN
        CREATE POLICY "Users can upload their own processed documents" ON storage.objects
            FOR INSERT WITH CHECK (bucket_id = 'processed-documents' AND auth.uid()::text = (storage.foldername(name))[1]);
    END IF;
END $$;

do $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'objects' AND policyname = 'Users can view their own processed documents') THEN
        CREATE POLICY "Users can view their own processed documents" ON storage.objects
            FOR SELECT USING (bucket_id = 'processed-documents' AND auth.uid()::text = (storage.foldername(name))[1]);
    END IF;
END $$;

do $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'objects' AND policyname = 'Users can update their own processed documents') THEN
        CREATE POLICY "Users can update their own processed documents" ON storage.objects
            FOR UPDATE USING (bucket_id = 'processed-documents' AND auth.uid()::text = (storage.foldername(name))[1]);
    END IF;
END $$;

do $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'objects' AND policyname = 'Users can delete their own processed documents') THEN
        CREATE POLICY "Users can delete their own processed documents" ON storage.objects
            FOR DELETE USING (bucket_id = 'processed-documents' AND auth.uid()::text = (storage.foldername(name))[1]);
    END IF;
END $$;

do $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'objects' AND policyname = 'Service role can manage all processed documents') THEN
        CREATE POLICY "Service role can manage all processed documents" ON storage.objects
            FOR ALL USING (bucket_id = 'processed-documents' AND auth.role() = 'service_role');
    END IF;
END $$;

-- ===================================================================
-- PERMISSIONS
-- ===================================================================
-- Grant necessary permissions
grant USAGE on SCHEMA public to anon,
authenticated;

grant all on all TABLES in SCHEMA public to service_role;

grant all on all SEQUENCES in SCHEMA public to service_role;

grant all on all FUNCTIONS in SCHEMA public to service_role;

-- ===================================================================
-- SUCCESS MESSAGE
-- ===================================================================
select
  'FinScope database schema created successfully! All tables, indexes, triggers, and RLS policies are ready.' as status;