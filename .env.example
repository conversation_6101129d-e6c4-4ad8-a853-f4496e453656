# Server Configuration
PORT=8080
HOST=0.0.0.0
NODE_ENV=development

# Frontend URL (for CORS)
FRONTEND_URL=http://localhost:5173

# Google Gemini AI
GEMINI_API_KEY='AIzaSyAy70R_pXWTyfeXk0ZRwRKoxXRCdKSzouY'

# Supabase Configuration (New API Key Format)
SUPABASE_URL='https://iaqfusmumkdifdrctzma.supabase.co'
SUPABASE_PUBLISHABLE_KEY='sb_publishable_your_new_publishable_key_here'
SUPABASE_SECRET_KEY='sb_secret_your_new_secret_key_here'

# Frontend Supabase Configuration (New API Key Format)
VITE_SUPABASE_URL='https://iaqfusmumkdifdrctzma.supabase.co'
VITE_SUPABASE_PUBLISHABLE_KEY='sb_publishable_your_new_publishable_key_here'

# Legacy Supabase Configuration (for migration reference)
SUPABASE_PROJECT_REF=iaqfusmumkdifdrctzma
SUPABASE_ACCESS_TOKEN='********************************************'


# File Upload Configuration
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/jpg,image/png,image/webp,application/pdf 


# Upstash
UPSTASH_REDIS_REST_URL=https://gusc1-dear-sponge-32157.upstash.io`
UPSTASH_REDIS_REST_TOKEN='AX2dASQgZTI4ZjM5NjgtZTAwMC00YjFiLThkZjYtMGZhMGMzZWU3ZDIzYTkzMjQ5MGE1YjY4NDY1NTkyNmRkMDRjZGU4MjMwM2M='

Legacy Redis Configuration (for compatibility)
REDIS_HOST='https://gusc1-dear-sponge-32157.upstash.io'
REDIS_PORT=443
REDIS_PASSWORD="AX2dASQgZTI4ZjM5NjgtZTAwMC00YjFiLThkZjYtMGZhMGMzZWU3ZDIzYTkzMjQ5MGE1YjY4NDY1NTkyNmRkMDRjZGU4MjMwM2M="
REDIS_DB=0

# Job Queue Configuration
QUEUE_CONCURRENCY=2
QUEUE_RETRY_ATTEMPTS=3
QUEUE_RETRY_DELAY=2000

# Processing Configuration
MAX_FILE_SIZE=10485760
ALLOWED_MIME_TYPES=image/png,image/jpeg,image/jpg,application/pdf,text/csv,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel

# Logging Configuration
LOG_LEVEL=info
ENABLE_AUDIT_TRAIL=true
ENABLE_PERFORMANCE_LOGGING=true