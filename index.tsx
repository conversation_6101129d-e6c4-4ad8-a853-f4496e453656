
// Import Supabase client
import { createClient } from '@supabase/supabase-js';

import { TransactionService, type Transaction as DatabaseTransaction } from "./services/database";

// Supabase configuration
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
    throw new Error('Missing Supabase environment variables');
}

// Initialize Supabase
const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Global state
let isProcessingAnalysis = false; // To prevent multiple simultaneous analyses
let currentUser: any = null;
let userTransactions: DatabaseTransaction[] = [];

// --- FinScope Core AI Definitions ---
// AI processing is now handled by the backend document processor service
// Removed redundant prompts - they are managed centrally in the backend



// DOM Elements
const signOutButton = document.getElementById('sign-out-button') as HTMLButtonElement;
const googleSignInButton = document.getElementById('google-signin-button') as HTMLButtonElement;

const screenAuth = document.getElementById('screen-auth') as HTMLElement;
const mainAppContainer = document.getElementById('main-app-container') as HTMLElement;
const bottomNavBar = document.getElementById('bottom-nav-bar') as HTMLElement;

const appScreens = document.querySelectorAll('.app-screen') as NodeListOf<HTMLElement>;
const navButtons = document.querySelectorAll('.nav-button') as NodeListOf<HTMLButtonElement>;

const closeModalButtons = document.querySelectorAll('.close-modal-button') as NodeListOf<HTMLButtonElement>;
const reviewAnalysisModal = document.getElementById('review-analysis-modal') as HTMLElement;

// Upload Screen Elements
const fileInput = document.getElementById('file-input') as HTMLInputElement;
const textInput = document.getElementById('text-input') as HTMLTextAreaElement;
const analyzeButton = document.getElementById('analyze-button') as HTMLButtonElement;
const analysisStatus = document.getElementById('analysis-status') as HTMLElement;

// API Key Handling - Now managed by backend services

// --- AI processing is now handled by the backend document processor service ---

// UI Helper
function showLoading(message: string, type: 'processing' | 'success' | 'error' = 'processing') {
    if (analysisStatus) {
        analysisStatus.textContent = message;
        analysisStatus.className = type;
    }
}

// Helper function to navigate between screens
function navigateToScreen(screenId: string): void {
    appScreens.forEach(screen => {
        if (screen.id === `screen-${screenId}`) {
            screen.classList.remove('hidden');
        } else {
            screen.classList.add('hidden');
        }
    });

    navButtons.forEach(button => {
        if (button.dataset.screen === screenId) {
            button.classList.add('active');
            button.setAttribute('aria-current', 'page');
        } else {
            button.classList.remove('active');
            button.removeAttribute('aria-current');
        }
    });
}

// Authentication State Listener
supabase.auth.onAuthStateChange(async (_event, session) => {
    if (session?.user) {
        currentUser = session.user;
        screenAuth.classList.add('hidden');
        mainAppContainer.classList.remove('hidden');
        bottomNavBar.classList.remove('hidden');
        signOutButton.classList.remove('hidden');

        // Load user's transactions from Supabase
        try {
            await loadUserTransactions();
        } catch (error) {
            console.error('Failed to load transactions:', error);
            showLoading('Failed to load your transactions. Please try refreshing the page.', 'error');
        }

        navigateToScreen('dashboard');
    } else {
        currentUser = null;
        userTransactions = [];
        screenAuth.classList.remove('hidden');
        mainAppContainer.classList.add('hidden');
        bottomNavBar.classList.add('hidden');
        signOutButton.classList.add('hidden');
    }
});

// Event Listeners for Auth
if (googleSignInButton) {
    googleSignInButton.addEventListener('click', async () => {
        try {
            const { error } = await supabase.auth.signInWithOAuth({
                provider: 'google',
                options: {
                    redirectTo: window.location.origin
                }
            });

            if (error) {
                throw error;
            }
        } catch (error) {
            console.error("Error during Google Sign-In: ", error);
            showLoading(`Sign-in error: ${error instanceof Error ? error.message : String(error)}`, 'error');
        }
    });
}

if (signOutButton) {
    signOutButton.addEventListener('click', async () => {
        try {
            const { error } = await supabase.auth.signOut();
            if (error) {
                throw error;
            }
        } catch (error) {
            console.error("Error during Sign-Out: ", error);
            showLoading(`Sign-out error: ${error instanceof Error ? error.message : String(error)}`, 'error');
        }
    });
}

// Event Listeners for Navigation
navButtons.forEach(button => {
    button.addEventListener('click', () => {
        const screenId = button.dataset.screen;
        if (screenId) {
            navigateToScreen(screenId);
        }
    });
});

// Event Listeners for Modals
closeModalButtons.forEach(button => {
    button.addEventListener('click', () => {
        const modal = button.closest('.modal') as HTMLElement;
        if (modal) {
            modal.classList.add('hidden');
        }
    });
});

// --- File Handling Helpers ---


// --- Database Functions ---
async function loadUserTransactions(): Promise<void> {
    if (!currentUser) return;
    
    try {
        userTransactions = await TransactionService.getTransactions(currentUser.uid);
        console.log(`Loaded ${userTransactions.length} transactions for user ${currentUser.uid}`);
        refreshAllDataViews();
    } catch (error) {
        console.error('Error loading transactions:', error);
        throw error;
    }
}

async function saveTransactionsToDatabase(transactions: DatabaseTransaction[]): Promise<void> {
    if (!currentUser) {
        throw new Error('User not authenticated');
    }

    try {
        // Add user_id to each transaction
        const transactionsWithUserId = transactions.map(tx => ({
            ...tx,
            user_id: currentUser.uid
        }));

        const savedTransactions = await TransactionService.addTransactions(transactionsWithUserId);
        console.log(`Saved ${savedTransactions.length} transactions to database`);
        
        // Reload transactions to update the UI
        await loadUserTransactions();
    } catch (error) {
        console.error('Error saving transactions:', error);
        throw error;
    }
}

function refreshAllDataViews(): void {
    // Update dashboard
    updateDashboard();
    // Update transactions screen
    updateTransactionsScreen();
    // Update insights screen
    updateInsightsScreen();
}

function updateDashboard(): void {
    const dashboardScreen = document.getElementById('screen-dashboard');
    if (!dashboardScreen) return;

    // Calculate basic stats
    const totalIncome = userTransactions
        .filter(t => t.type === 'credit')
        .reduce((sum, t) => sum + t.amount, 0);
    
    const totalExpenses = userTransactions
        .filter(t => t.type === 'debit')
        .reduce((sum, t) => sum + t.amount, 0);

    const netCashFlow = totalIncome - totalExpenses;

    // Update dashboard content
    dashboardScreen.innerHTML = `
        <h2 id="dashboard-heading">Dashboard</h2>
        <div class="dashboard-stats">
            <div class="stat-card">
                <h3>Money In</h3>
                <p class="amount positive">₦${totalIncome.toLocaleString()}</p>
            </div>
            <div class="stat-card">
                <h3>Money Out</h3>
                <p class="amount negative">₦${totalExpenses.toLocaleString()}</p>
            </div>
            <div class="stat-card">
                <h3>Net Cash Flow</h3>
                <p class="amount ${netCashFlow >= 0 ? 'positive' : 'negative'}">₦${netCashFlow.toLocaleString()}</p>
            </div>
        </div>
        <div class="recent-transactions">
            <h3>Recent Transactions</h3>
            ${userTransactions.slice(0, 5).map(tx => `
                <div class="transaction-item">
                    <span class="transaction-date">${tx.date}</span>
                    <span class="transaction-description">${tx.description}</span>
                    <span class="transaction-amount ${tx.type === 'credit' ? 'positive' : 'negative'}">
                        ${tx.type === 'credit' ? '+' : '-'}₦${tx.amount.toLocaleString()}
                    </span>
                </div>
            `).join('')}
        </div>
    `;
}

function updateTransactionsScreen(): void {
    const transactionsScreen = document.getElementById('screen-transactions');
    if (!transactionsScreen) return;

    transactionsScreen.innerHTML = `
        <h2 id="transactions-heading">Transactions</h2>
        <div class="transactions-controls">
            <input type="text" id="search-transactions" placeholder="Search transactions...">
            <select id="category-filter">
                <option value="">All Categories</option>
                <option value="Add Money (Wallet Top-Up)">Add Money (Wallet Top-Up)</option>
                <option value="Bank Deposit">Bank Deposit</option>
                <option value="Cash Deposit">Cash Deposit</option>
                <option value="Interest Earned">Interest Earned</option>
                <option value="Investment Payback">Investment Payback</option>
                <option value="Refund">Refund</option>
                <option value="Loan Disbursement">Loan Disbursement</option>
                <option value="Commission">Commission</option>
                <option value="Utilities">Utilities</option>
                <option value="Airtime">Airtime</option>
                <option value="Data Bundle">Data Bundle</option>
                <option value="TV & Subscriptions">TV & Subscriptions</option>
                <option value="Online Shopping">Online Shopping</option>
                <option value="Card Payment">Card Payment</option>
                <option value="QR Code Payment">QR Code Payment</option>
                <option value="Merchant Payment">Merchant Payment</option>
                <option value="Business Payments">Business Payments</option>
                <option value="Invoice Payments">Invoice Payments</option>
                <option value="Savings & Investments">Savings & Investments</option>
                <option value="Loan Repayment">Loan Repayment</option>
                <option value="Money Transfer">Money Transfer</option>
                <option value="Financial Institution Payment">Financial Institution Payment</option>
                <option value="Transportation & Tolls">Transportation & Tolls</option>
                <option value="Travel & Hotel">Travel & Hotel</option>
                <option value="Education">Education</option>
                <option value="Events & Entertainment">Events & Entertainment</option>
                <option value="Betting">Betting</option>
                <option value="Donations & Dues">Donations & Dues</option>
                <option value="Government Payments">Government Payments</option>
                <option value="Bank Charges">Bank Charges</option>
                <option value="Cash Withdrawal">Cash Withdrawal</option>
                <option value="Other">Other</option>
            </select>
        </div>
        <div class="transactions-list">
            ${userTransactions.map(tx => `
                <div class="transaction-item" data-id="${tx.id}">
                    <div class="transaction-info">
                        <span class="transaction-date">${tx.date}</span>
                        <span class="transaction-description">${tx.description}</span>
                        <span class="transaction-category">${tx.category || 'Uncategorized'}</span>
                    </div>
                    <div class="transaction-actions">
                        <span class="transaction-amount ${tx.type === 'credit' ? 'positive' : 'negative'}">
                            ${tx.type === 'credit' ? '+' : '-'}₦${tx.amount.toLocaleString()}
                        </span>
                        <button class="edit-transaction-btn" onclick="editTransaction('${tx.id}')">Edit</button>
                        <button class="delete-transaction-btn" onclick="deleteTransaction('${tx.id}')">Delete</button>
                    </div>
                </div>
            `).join('')}
        </div>
    `;
}

function updateInsightsScreen(): void {
    const insightsScreen = document.getElementById('screen-insights');
    if (!insightsScreen) return;

    // Calculate insights
    const categoryBreakdown: Record<string, number> = {};
    userTransactions.forEach(tx => {
        if (tx.category) {
            categoryBreakdown[tx.category] = (categoryBreakdown[tx.category] || 0) + tx.amount;
        }
    });

    const topSpendingCategory = Object.entries(categoryBreakdown)
        .sort(([,a], [,b]) => b - a)[0];

    const largestExpense = userTransactions
        .filter(t => t.type === 'debit')
        .sort((a, b) => b.amount - a.amount)[0];

    insightsScreen.innerHTML = `
        <h2 id="insights-heading">Financial Insights</h2>
        <div class="insights-grid">
            <div class="insight-card">
                <h3>Top Spending Category</h3>
                <p>${topSpendingCategory ? topSpendingCategory[0] : 'No data'}</p>
                <p class="amount">₦${topSpendingCategory ? topSpendingCategory[1].toLocaleString() : '0'}</p>
            </div>
            <div class="insight-card">
                <h3>Largest Single Expense</h3>
                <p>${largestExpense ? largestExpense.description : 'No data'}</p>
                <p class="amount negative">₦${largestExpense ? largestExpense.amount.toLocaleString() : '0'}</p>
            </div>
            <div class="insight-card">
                <h3>Total Transactions</h3>
                <p class="amount">${userTransactions.length}</p>
            </div>
        </div>
    `;
}

// --- Main Analysis Logic (Phase 2) ---
if (analyzeButton) {
    analyzeButton.addEventListener('click', async () => {
        if (isProcessingAnalysis) {
            showLoading("Analysis is already in progress. Please wait.", 'error');
            return;
        }


        const pastedText = textInput.value.trim();
        const selectedFile = fileInput.files ? fileInput.files[0] : null;

        if (!pastedText && !selectedFile) {
            showLoading("Please paste text or select a file to analyze.", 'error');
            return;
        }

        isProcessingAnalysis = true;
        analyzeButton.disabled = true;
        showLoading("Preparing data...");


        let extractedTransactions: DatabaseTransaction[] = []; // Matches the DatabaseTransaction interface which includes optional category
        let detectedBank: string | undefined = "Unknown";

        try {
            // --- Document Processing via Backend API ---
            showLoading("Processing document with AI...");
            
            let response: any;
            
            if (pastedText) {
                // Process text input
                const formData = new FormData();
                formData.append('text', pastedText);
                formData.append('userId', currentUser.uid);
                formData.append('analysisMode', 'advanced');
                
                response = await fetch('/api/v1/documents/process', {
                    method: 'POST',
                    body: formData
                });
            } else if (selectedFile) {
                // Process file upload
                const formData = new FormData();
                formData.append('file', selectedFile);
                formData.append('userId', currentUser.uid);
                formData.append('analysisMode', 'advanced');
                
                response = await fetch('/api/v1/documents/process', {
                    method: 'POST',
                    body: formData
                });
            } else {
                throw new Error("No input provided.");
            }
            
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
            }
            
            const result = await response.json();
            
            if (!result.success) {
                throw new Error(result.error || 'Document processing failed');
            }
            
            // Extract transactions from the response
            extractedTransactions = result.data.transactions.map((tx: any) => ({
                ...tx,
                user_id: currentUser.uid
            }));
            
            detectedBank = result.data.detectedBank || "Unknown";
            
            // Confirm/Correct Bank
            const userConfirmedBank = prompt(`Detected bank: ${detectedBank}. If this is incorrect, please enter the correct bank name, otherwise press OK.`, detectedBank);
            if (userConfirmedBank !== null && userConfirmedBank.trim() !== "") {
                detectedBank = userConfirmedBank.trim();
                // Update source_bank in all transactions if user provided a new one
                extractedTransactions.forEach(tx => tx.source_bank = detectedBank!);
            }


            // --- Completion ---
            showLoading(`Analysis complete! ${extractedTransactions.length} transactions processed. Detected bank: ${detectedBank}. Saving to database...`, 'success');
            console.log("Analysis Complete. Result:", extractedTransactions);
            
            // Save transactions to Supabase database
            try {
                await saveTransactionsToDatabase(extractedTransactions);
                showLoading(`Successfully saved ${extractedTransactions.length} transactions to your account!`, 'success');
                
                // Show the review modal
            if (reviewAnalysisModal) {
                reviewAnalysisModal.classList.remove('hidden');
                }
            } catch (error) {
                console.error('Failed to save transactions:', error);
                showLoading(`Analysis completed but failed to save to database: ${error instanceof Error ? error.message : String(error)}`, 'error');
            }
            
            // Clear inputs after successful analysis
            textInput.value = '';
            fileInput.value = '';

        } catch (error) {
            console.error("Error during analysis:", error);
            showLoading(`Analysis Error: ${error instanceof Error ? error.message : String(error)}`, 'error');
        } finally {
            isProcessingAnalysis = false;
            analyzeButton.disabled = false;
        }
    });
}

console.log("FinScope Phase 2 (Single-Threaded) Initialized.");
// Note: API_KEY (process.env.API_KEY) must be available for analysis to work.
// This needs to be configured in the execution environment.

// Global functions for transaction management (accessible from HTML)
(window as any).editTransaction = async function(transactionId: string) {
    const transaction = userTransactions.find(t => t.id === transactionId);
    if (!transaction) {
        alert('Transaction not found');
        return;
    }
    
    // For now, just show an alert. You can implement a proper edit modal later
    const newAmount = prompt('Enter new amount:', transaction.amount.toString());
    if (newAmount === null) return;
    
    const newDescription = prompt('Enter new description:', transaction.description);
    if (newDescription === null) return;
    
    try {
        await TransactionService.updateTransaction(transactionId, {
            amount: parseFloat(newAmount),
            description: newDescription
        });
        
        // Reload transactions
        await loadUserTransactions();
        showLoading('Transaction updated successfully!', 'success');
    } catch (error) {
        console.error('Failed to update transaction:', error);
        showLoading('Failed to update transaction', 'error');
    }
};

(window as any).deleteTransaction = async function(transactionId: string) {
    if (!confirm('Are you sure you want to delete this transaction?')) {
        return;
    }
    
    try {
        await TransactionService.deleteTransaction(transactionId);
        
        // Reload transactions
        await loadUserTransactions();
        showLoading('Transaction deleted successfully!', 'success');
    } catch (error) {
        console.error('Failed to delete transaction:', error);
        showLoading('Failed to delete transaction', 'error');
    }
};
