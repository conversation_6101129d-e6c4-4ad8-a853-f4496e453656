"""
Supabase Client for PDF Processor Service

This module provides Supabase integration for the Python PDF processor,
replacing Firebase Storage and Firestore functionality.
"""

import os
import json
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
import requests
from io import BytesIO

logger = logging.getLogger(__name__)

class SupabaseClient:
    """Supabase client for PDF processor operations"""
    
    def __init__(self):
        self.url = os.getenv('SUPABASE_URL')
        self.secret_key = os.getenv('SUPABASE_SECRET_KEY')
        self.publishable_key = os.getenv('SUPABASE_PUBLISHABLE_KEY')

        if not self.url or not self.secret_key:
            raise ValueError("Missing required Supabase environment variables")
        
        self.headers = {
            'apikey': self.secret_key,
            'Authorization': f'Bearer {self.secret_key}',
            'Content-Type': 'application/json',
            'Prefer': 'return=representation'
        }

        self.storage_headers = {
            'apikey': self.secret_key,
            'Authorization': f'Bearer {self.secret_key}'
        }
    
    def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None, 
                     headers: Optional[Dict] = None) -> requests.Response:
        """Make HTTP request to Supabase API"""
        url = f"{self.url}/rest/v1/{endpoint}"
        request_headers = headers or self.headers
        
        try:
            response = requests.request(
                method=method,
                url=url,
                headers=request_headers,
                json=data,
                timeout=30
            )
            response.raise_for_status()
            return response
        except requests.exceptions.RequestException as e:
            logger.error(f"Supabase API request failed: {e}")
            raise
    
    def _make_storage_request(self, method: str, endpoint: str, data: Optional[bytes] = None,
                            headers: Optional[Dict] = None) -> requests.Response:
        """Make HTTP request to Supabase Storage API"""
        url = f"{self.url}/storage/v1/{endpoint}"
        request_headers = headers or self.storage_headers.copy()
        
        try:
            response = requests.request(
                method=method,
                url=url,
                headers=request_headers,
                data=data,
                timeout=60
            )
            response.raise_for_status()
            return response
        except requests.exceptions.RequestException as e:
            logger.error(f"Supabase Storage request failed: {e}")
            raise
    
    # Storage Operations
    def upload_file(self, file_data: bytes, file_path: str, content_type: str = 'application/octet-stream') -> str:
        """
        Upload file to Supabase Storage
        
        Args:
            file_data: File content as bytes
            file_path: Path in storage bucket (e.g., 'documents/user123/file.pdf')
            content_type: MIME type of the file
            
        Returns:
            str: Public URL of uploaded file
        """
        try:
            # Upload to documents bucket
            headers = self.storage_headers.copy()
            headers['Content-Type'] = content_type
            
            response = self._make_storage_request(
                'POST',
                f'object/documents/{file_path}',
                data=file_data,
                headers=headers
            )
            
            # Get public URL
            public_url = f"{self.url}/storage/v1/object/public/documents/{file_path}"
            
            logger.info(f"File uploaded successfully: {file_path}")
            return public_url
            
        except Exception as e:
            logger.error(f"Failed to upload file {file_path}: {e}")
            raise
    
    def download_file(self, file_path: str) -> bytes:
        """
        Download file from Supabase Storage
        
        Args:
            file_path: Path in storage bucket
            
        Returns:
            bytes: File content
        """
        try:
            response = self._make_storage_request(
                'GET',
                f'object/documents/{file_path}'
            )
            
            return response.content
            
        except Exception as e:
            logger.error(f"Failed to download file {file_path}: {e}")
            raise
    
    def delete_file(self, file_path: str) -> bool:
        """
        Delete file from Supabase Storage
        
        Args:
            file_path: Path in storage bucket
            
        Returns:
            bool: True if successful
        """
        try:
            self._make_storage_request(
                'DELETE',
                f'object/documents/{file_path}'
            )
            
            logger.info(f"File deleted successfully: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete file {file_path}: {e}")
            return False
    
    def list_files(self, prefix: str = '') -> List[Dict]:
        """
        List files in storage bucket
        
        Args:
            prefix: Path prefix to filter files
            
        Returns:
            List[Dict]: List of file objects
        """
        try:
            endpoint = f'object/list/documents'
            if prefix:
                endpoint += f'?prefix={prefix}'
            
            response = self._make_storage_request('POST', endpoint)
            return response.json()
            
        except Exception as e:
            logger.error(f"Failed to list files with prefix {prefix}: {e}")
            return []
    
    # Database Operations
    def insert_processing_log(self, log_data: Dict) -> Dict:
        """Insert processing log into database"""
        try:
            response = self._make_request('POST', 'processing_logs', data=log_data)
            return response.json()[0] if response.json() else {}
        except Exception as e:
            logger.error(f"Failed to insert processing log: {e}")
            raise
    
    def insert_error_log(self, error_data: Dict) -> Dict:
        """Insert error log into database"""
        try:
            response = self._make_request('POST', 'error_logs', data=error_data)
            return response.json()[0] if response.json() else {}
        except Exception as e:
            logger.error(f"Failed to insert error log: {e}")
            raise
    
    def insert_performance_log(self, perf_data: Dict) -> Dict:
        """Insert performance log into database"""
        try:
            response = self._make_request('POST', 'performance_logs', data=perf_data)
            return response.json()[0] if response.json() else {}
        except Exception as e:
            logger.error(f"Failed to insert performance log: {e}")
            raise
    
    def update_job_status(self, job_id: str, updates: Dict) -> Dict:
        """Update job status in database"""
        try:
            response = self._make_request(
                'PATCH', 
                f'job_status?job_id=eq.{job_id}', 
                data=updates
            )
            return response.json()[0] if response.json() else {}
        except Exception as e:
            logger.error(f"Failed to update job status {job_id}: {e}")
            raise
    
    def get_job_status(self, job_id: str) -> Optional[Dict]:
        """Get job status from database"""
        try:
            response = self._make_request('GET', f'job_status?job_id=eq.{job_id}')
            data = response.json()
            return data[0] if data else None
        except Exception as e:
            logger.error(f"Failed to get job status {job_id}: {e}")
            return None
    
    # Utility methods
    def log_processing(self, user_id: str, filename: str, operation: str, 
                      success: bool, metadata: Dict) -> None:
        """Log processing operation (replacement for Firebase logging)"""
        try:
            log_data = {
                'user_id': user_id,
                'operation': operation,
                'status': 'completed' if success else 'failed',
                'file_name': filename,
                'file_size': metadata.get('file_size'),
                'mime_type': metadata.get('mime_type'),
                'transaction_count': metadata.get('transaction_count'),
                'confidence_score': metadata.get('confidence_score'),
                'duration_ms': metadata.get('duration_ms'),
                'error_message': metadata.get('error_message') if not success else None,
                'metadata': metadata,
                'timestamp': datetime.utcnow().isoformat(),
                'created_at': datetime.utcnow().isoformat()
            }
            
            self.insert_processing_log(log_data)
            
        except Exception as e:
            logger.error(f"Failed to log processing operation: {e}")
    
    def log_error(self, error_name: str, error_message: str, error_stack: Optional[str] = None,
                  context: Optional[Dict] = None, user_id: Optional[str] = None, request_id: Optional[str] = None) -> None:
        """Log error to database"""
        try:
            error_data = {
                'error_name': error_name or '',
                'error_message': error_message or '',
                'error_stack': error_stack or '',
                'context': context or {},
                'user_id': user_id or '',
                'request_id': request_id or '',
                'timestamp': datetime.utcnow().isoformat(),
                'created_at': datetime.utcnow().isoformat()
            }
            self.insert_error_log(error_data)
        except Exception as e:
            logger.error(f"Failed to log error: {e}")

    def log_performance(self, operation: str, duration: float, metadata: Optional[Dict] = None,
                       user_id: Optional[str] = None) -> None:
        """Log performance metrics"""
        try:
            perf_data = {
                'operation': operation or '',
                'duration': duration,
                'metadata': metadata or {},
                'user_id': user_id or '',
                'timestamp': datetime.utcnow().isoformat(),
                'created_at': datetime.utcnow().isoformat()
            }
            self.insert_performance_log(perf_data)
        except Exception as e:
            logger.error(f"Failed to log performance: {e}")

# Global instance
supabase_client = None

def get_supabase_client() -> SupabaseClient:
    """Get or create Supabase client instance"""
    global supabase_client
    if supabase_client is None:
        supabase_client = SupabaseClient()
    return supabase_client
