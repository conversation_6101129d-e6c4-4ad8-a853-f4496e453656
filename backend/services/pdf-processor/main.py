from fastapi import <PERSON><PERSON><PERSON>, File, UploadFile, HTTPException
from fastapi.responses import JSONResponse
import pdfplumber
import os

app = FastAPI()

@app.get("/")
def read_root():
    return {"message": "PDF Processor is running."}

@app.post("/extract-text")
async def extract_text(file: UploadFile = File(...)):
    # Validate file type
    if not file.filename.lower().endswith('.pdf'):
        raise HTTPException(status_code=400, detail="Only PDF files are supported.")
    try:
        # Use pdfplumber to extract text from the uploaded PDF
        with pdfplumber.open(file.file) as pdf:
            text = "\n".join(page.extract_text() or '' for page in pdf.pages)
        if not text.strip():
            return JSONResponse(status_code=204, content={"message": "No text found in PDF."})
        return {"text": text}
    except Exception as e:
        # Handle errors (e.g., invalid/corrupt PDF)
        raise HTTPException(status_code=500, detail=f"Failed to extract text: {str(e)}")