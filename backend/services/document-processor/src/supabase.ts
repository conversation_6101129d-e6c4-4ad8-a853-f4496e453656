import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { Transaction, TransactionStats } from './types';

let supabase: SupabaseClient;

export function initializeSupabase() {
  const supabaseUrl = process.env.SUPABASE_URL;
  const supabaseSecretKey = process.env.SUPABASE_SECRET_KEY;

  if (!supabaseUrl) {
    console.error('SUPABASE_URL is not set in environment variables');
    console.log('Available env vars:', Object.keys(process.env).filter(key => key.includes('SUPABASE')));
    throw new Error('SUPABASE_URL is required');
  }

  if (!supabaseSecretKey) {
    console.error('SUPABASE_SECRET_KEY is not set in environment variables');
    throw new Error('SUPABASE_SECRET_KEY is required');
  }

  console.log('Initializing Supabase client with URL:', supabaseUrl);
  supabase = createClient(supabaseUrl, supabaseSecretKey);
}

export function getSupabaseClient(): SupabaseClient {
  if (!supabase) {
    throw new Error('Supabase client not initialized. Call initializeSupabase() first.');
  }
  return supabase;
}

const TRANSACTIONS_TABLE = 'transactions';

export class SupabaseService {
  // Get all transactions for a user
  static async getTransactions(userId: string): Promise<Transaction[]> {
    try {
      const { data, error } = await getSupabaseClient()
        .from(TRANSACTIONS_TABLE)
        .select('*')
        .eq('user_id', userId)
        .order('date', { ascending: false });

      if (error) {
        console.error('Error fetching transactions:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Failed to fetch transactions:', error);
      throw error;
    }
  }

  // Add multiple transactions in a batch
  static async addTransactions(transactions: Omit<Transaction, 'id' | 'created_at' | 'updated_at'>[]): Promise<Transaction[]> {
    try {
      const { data, error } = await getSupabaseClient()
        .from(TRANSACTIONS_TABLE)
        .insert(transactions)
        .select();

      if (error) {
        console.error('Error adding transactions:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Failed to add transactions:', error);
      throw error;
    }
  }

  // Update a transaction
  static async updateTransaction(id: string, updates: Partial<Transaction>): Promise<Transaction> {
    try {
      const { data, error } = await getSupabaseClient()
        .from(TRANSACTIONS_TABLE)
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Error updating transaction:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Failed to update transaction:', error);
      throw error;
    }
  }

  // Delete a transaction
  static async deleteTransaction(id: string): Promise<void> {
    try {
      const { error } = await getSupabaseClient()
        .from(TRANSACTIONS_TABLE)
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting transaction:', error);
        throw error;
      }
    } catch (error) {
      console.error('Failed to delete transaction:', error);
      throw error;
    }
  }

  // Get transaction statistics
  static async getTransactionStats(userId: string, startDate?: string, endDate?: string): Promise<TransactionStats> {
    try {
      let query = getSupabaseClient()
        .from(TRANSACTIONS_TABLE)
        .select('*')
        .eq('user_id', userId);

      if (startDate && endDate) {
        query = query.gte('date', startDate).lte('date', endDate);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching transaction stats:', error);
        throw error;
      }

      const transactions = data || [];
      const totalIncome = transactions
        .filter(t => t.type === 'credit')
        .reduce((sum, t) => sum + t.amount, 0);
      
      const totalExpenses = transactions
        .filter(t => t.type === 'debit')
        .reduce((sum, t) => sum + t.amount, 0);

      const categoryBreakdown: Record<string, number> = {};
      transactions.forEach(t => {
        if (t.category) {
          categoryBreakdown[t.category] = (categoryBreakdown[t.category] || 0) + t.amount;
        }
      });

      return {
        totalIncome,
        totalExpenses,
        netCashFlow: totalIncome - totalExpenses,
        transactionCount: transactions.length,
        categoryBreakdown
      };
    } catch (error) {
      console.error('Failed to get transaction stats:', error);
      throw error;
    }
  }
} 