import { v4 as uuidv4 } from 'uuid';
import { 
  Transaction, 
  ExtractedData, 
  ProcessingResult, 
  ConfidenceScore,

  ProcessingLog,

  ProcessingOptions,

  SupabaseInsertResult
} from './types';
import { SupabaseDocumentService } from './utils/supabase-service';
import { supabaseClient, insertTransactions } from './utils/supabase';
import { GeminiService } from './utils/gemini';
import { FileRouter } from './utils/fileRouter';

interface DocumentMetadata {
  documentType: 'receipt' | 'bank_statement' | 'transaction_slip' | 'unknown';
  source: string;
  timestamp: Date;
  fileSize: number;
  mimeType: string;
}

export class DocumentProcessor {
  private static isInitialized = false;

  static initialize() {
    if (this.isInitialized) return;

    try {
      // Initialize all services
      SupabaseDocumentService.initialize();
      // SupabaseService.initialize(); // No longer needed with singleton pattern
      GeminiService.initialize();

      this.isInitialized = true;
      console.log('✅ DocumentProcessor initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize DocumentProcessor:', error);
      throw error;
    }
  }

  // Main processing pipeline
  static async processDocument(
    fileBuffer: Buffer,
    mimeType: string,
    userId: string,
    fileName: string,
    options: ProcessingOptions = {}
  ): Promise<ProcessingResult> {
    this.initialize();

    const processingId = uuidv4();
    const batchId = options.batchId || uuidv4();
    const startTime = Date.now();

    // Default options
    const defaultOptions: ProcessingOptions = {
      enableAdvancedAnalysis: false,
      enableDeduplication: true,
      enableRetry: true,
      maxRetries: 3,
      batchSize: 100,
      enableLogging: true,
      enableAuditTrail: true,
      ...options
    };

    let retries = 0;
    let lastError: any = null;
    while (retries <= defaultOptions.maxRetries!) {
    try {
      // Log processing start
      if (defaultOptions.enableLogging) {
        await this.logProcessingStart(processingId, batchId, userId, fileName);
      }

      console.log(`🔄 Processing document: ${fileName} (${mimeType}), size: ${fileBuffer.length} bytes`);

      // Step 1: Analyze document type and extract basic metadata
      const metadata = await this.analyzeDocumentType(fileBuffer, mimeType, processingId);
      console.log(`📄 Detected document type: ${metadata.documentType}`);

      // Step 2: Extract text using appropriate method
        const extractedText = await this.extractText(fileBuffer, mimeType, fileName, userId, processingId);
      console.log(`📝 Extracted text length: ${extractedText.length} characters`);

      // Step 3: Parse and structure the data
        let extractedData: ExtractedData;
        if (mimeType === 'text/csv' || mimeType === 'application/vnd.ms-excel' || mimeType === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
          extractedData = await this.parseStructuredTable(fileBuffer, mimeType, userId, processingId, batchId, fileName);
        } else {
          extractedData = await this.parseStructuredData(
        extractedText, 
        metadata, 
        processingId,
            userId,
        defaultOptions.enableAdvancedAnalysis
      );
        }
      console.log(`💳 Extracted ${extractedData.transactions.length} transactions`);

      // Step 4: Validate and enhance the data
      const validatedData = await this.validateAndEnhanceData(extractedData, userId, processingId);

      // Step 5: Calculate confidence scores
      const confidenceScores = this.calculateConfidenceScores(validatedData);

      // Step 6: Insert into Supabase
        const supabaseResult = await this.insertToSupabase(validatedData.transactions, userId, processingId, batchId);

      // Step 7: Log processing completion
      if (defaultOptions.enableLogging) {
        await this.logProcessingComplete(processingId, batchId, userId, fileName, {
          transactionCount: validatedData.transactions.length,
          insertedCount: supabaseResult.insertedCount,
          duplicateCount: supabaseResult.duplicateCount,
          errorCount: supabaseResult.errorCount,
          confidence: confidenceScores.overall
        });
          // Additive: also log to document_processing_logs table
          await this.logToSupabaseLogTable({
            processing_id: processingId,
            batch_id: batchId,
            user_id: userId,
            file_name: fileName,
            file_size: fileBuffer.length,
            mime_type: mimeType,
            status: 'completed',
            stage: 'supabase_insert',
            transactions_extracted: validatedData.transactions.length,
            transactions_inserted: supabaseResult.insertedCount,
            duplicate_count: supabaseResult.duplicateCount,
            error_count: supabaseResult.errorCount,
            processing_time_ms: Date.now() - startTime,
            document_type: metadata.documentType,
            source_bank: metadata.source,
            confidence_score: confidenceScores.overall,
            error_message: null,
            retry_count: retries,
            created_at: new Date().toISOString()
          });
      }

      const processingTime = Date.now() - startTime;

      return {
        success: true,
        metadata,
        extractedData: validatedData,
        confidenceScores,
        processingTime,
        rawText: extractedText.substring(0, 1000) + '...', // Truncated for storage
        batchId,
        processingId,
        supabaseResult
      };
    } catch (error) {
        lastError = error;
        retries++;
        if (!defaultOptions.enableRetry || retries > defaultOptions.maxRetries!) break;
        const backoff = Math.pow(2, retries) * 100;
        console.warn(`Retrying document processing (attempt ${retries}) after ${backoff}ms due to error:`, error);
        await new Promise(res => setTimeout(res, backoff));
      }
    }
      const processingTime = Date.now() - startTime;
    const errorMessage = lastError instanceof Error ? lastError.message : 'Unknown processing error';
      console.error('❌ Document processing failed:', errorMessage);
      if (defaultOptions.enableLogging) {
        await this.logProcessingError(processingId, batchId, userId, fileName, errorMessage);
      await this.logToSupabaseLogTable({
        processing_id: processingId,
        batch_id: batchId,
        user_id: userId,
        file_name: fileName,
        file_size: fileBuffer.length,
        mime_type: mimeType,
        status: 'failed',
        stage: 'document_analysis',
        transactions_extracted: 0,
        transactions_inserted: 0,
        duplicate_count: 0,
        error_count: 1,
        processing_time_ms: processingTime,
        document_type: null,
        source_bank: null,
        confidence_score: null,
        error_message: errorMessage,
        retry_count: retries,
        created_at: new Date().toISOString()
      });
    }
      return {
        success: false,
        error: errorMessage,
        processingTime,
        batchId,
        processingId
      };
  }

  // Step 1: Analyze document type and extract metadata
  private static async analyzeDocumentType(
    fileBuffer: Buffer,
    mimeType: string,
    _processingId: string
  ): Promise<DocumentMetadata> {
    const base64Image = fileBuffer.toString('base64');
    
    const prompt = `
    Analyze this financial document image and determine its type. Return a JSON response with:
    {
      "documentType": "receipt" | "bank_statement" | "transaction_slip" | "unknown",
      "bankName": "detected bank name or null",
      "currency": "detected currency code or null",
      "language": "detected language code or null",
      "confidence": 0.0-1.0
    }

    Document types:
    - "receipt": Point of sale receipts, merchant receipts, payment confirmations
    - "bank_statement": Bank account statements, transaction lists, balance sheets
    - "transaction_slip": ATM slips, transfer confirmations, payment slips
    - "unknown": Cannot determine document type
    `;

    try {
      const response = await GeminiService.generateContent(
        prompt,
        { data: base64Image, mimeType: mimeType },
        'gemini-1.5-flash'
      );

      if (!response.success) {
        throw new Error(`Document type analysis failed: ${response.error}`);
      }

      const analysis = response.data;
      
      return {
        documentType: analysis.documentType || 'unknown',
        source: analysis.bankName || 'Unknown',
        timestamp: new Date(),
        fileSize: fileBuffer.length,
        mimeType
      };
    } catch (error) {
      console.warn('Document type analysis failed, defaulting to unknown:', error);
      return {
        documentType: 'unknown',
        source: 'Unknown',
        timestamp: new Date(),
        fileSize: fileBuffer.length,
        mimeType
      };
    }
  }

  // Step 2: Extract text from document using PDF router
  private static async extractText(fileBuffer: Buffer, mimeType: string, fileName: string, userId: string, processingId: string): Promise<string> {
    try {
      // Use File router to determine the best extraction method
      const result = await FileRouter.routeDocument(fileBuffer, mimeType, fileName, userId, processingId);
      // Log the routing decision
      await FileRouter.logRoutingDecision(processingId, userId, fileName, result);
      if (!result.success) {
        throw new Error(`Text extraction failed: ${result.metadata.error}`);
      }
      console.log(`✅ Text extracted using ${result.method}: ${result.metadata.textLength} characters`);
      return result.text;
    } catch (error) {
      console.error('Text extraction failed:', error);
      throw new Error('Failed to extract text from document');
    }
  }

  // Step 3: Parse structured data from extracted text
  public static async parseStructuredData(
    extractedText: string,
    metadata: DocumentMetadata,
    _processingId: string,
    userId: string,
    enableAdvancedAnalysis: boolean = false
  ): Promise<ExtractedData> {


    // Token safety
    const safeText = `\
\
\
${extractedText.slice(0, 7000)}\
\
\
`;
    const prompt = (enableAdvancedAnalysis ? this.getAdvancedAnalysisPrompt() : this.getBasicAnalysisPrompt()) + `\n\nRaw Text:\n${safeText}`;
    try {
      const response = await GeminiService.generateContent(
        prompt,
        undefined,
        'gemini-1.5-flash'
      );
      if (!response.success) {
        throw new Error(`Structured data parsing failed: ${response.error}`);
      }
      const parsedData = response.data;
      if (!parsedData || !Array.isArray(parsedData.transactions)) {
        throw new Error('Missing transactions array in parsed data');
      }
      // Convert to our internal format with improved fingerprint generation
      const transactions: Transaction[] = parsedData.transactions.map((tx: any, index: number) => {
        // Deduplicate/standardize fields
        const vendor = tx.vendor ?? tx.merchant ?? tx.merchant_name ?? '';
        const narration = tx.narration ?? tx.description ?? '';
        return {
          id: `temp_${Date.now()}_${index}`,
          user_id: userId,
          fingerprint: this.generateFingerprint(tx, index),
          date: normalizeDate(tx.date) || '',
        time: tx.time || null,
          description: narration || 'Unknown transaction',
        amount: parseFloat(tx.amount) || 0,
        type: tx.type === 'credit' ? 'credit' : 'debit',
          category: this.categorizeTransaction(narration || ''),
          vendor: vendor ?? '',
          bank_provided_id: tx.reference ?? '',
          source_bank: tx.bankName ?? metadata.source ?? '',
        created_at: undefined,
        updated_at: undefined,
        // Advanced fields (only if enabled)
        ...(enableAdvancedAnalysis && {
          sender_name: tx.sender_name || undefined,
          receiver_name: tx.receiver_name || undefined,
          sender_account_number: tx.sender_account_number || undefined,
          receiver_account_number: tx.receiver_account_number || undefined,
          transaction_fee: tx.fees ? parseFloat(tx.fees) : undefined,
          currency_type: tx.currency || 'NGN',
          payment_method: tx.channel || undefined,
            narration: narration || '',
          authorization_code: tx.authorization_code || undefined,
          destination_bank: tx.destination_bank || undefined,
          balance_before: tx.balance_before ? parseFloat(tx.balance_before) : undefined,
          balance_after: tx.balance_after ? parseFloat(tx.balance_after) : undefined,
        })
        };
      });
      return {
        transactions,
        accountInfo: parsedData.accountInfo || {},
        summary: parsedData.summary || {},
        metadata: {
          documentType: metadata.documentType,
          source: metadata.source,
          processingTimestamp: new Date().toISOString(),
          confidence: 0.8 // Will be refined in validation step
        }
      };
    } catch (error) {
      console.error('Structured data parsing failed:', error);
      throw new Error('Failed to parse structured data from document');
    }
  }

  // New: Parse structured table (CSV/Excel) directly
  public static async parseStructuredTable(
    fileBuffer: Buffer,
    mimeType: string,
    userId: string,
    _processingId: string,
    batchId: string,
    _fileName: string
  ): Promise<ExtractedData> {
    let transactions: Transaction[] = [];
    if (mimeType === 'text/csv') {
      const csv = require('./utils/csv-service');
      let records;
      try {
        records = require('csv-parse/sync').parse(fileBuffer.toString(), { columns: true, skip_empty_lines: true, trim: true });
      } catch (err) {
        throw new Error(`CSV parsing failed: ${(err as Error).message}`);
      }
      transactions = csv.CSVService.extractTransactions(records).map((tx: any, index: number) => ({
        ...tx,
        id: undefined,
        user_id: userId,
        fingerprint: this.generateFingerprint(tx, index),
        date: normalizeDate(tx.date) || '',
        type: tx.type === 'credit' ? 'credit' : 'debit',
        category: this.categorizeTransaction(tx.description ?? ''),
        vendor: tx.vendor ?? tx.merchant ?? '',
        bank_provided_id: tx.reference ?? '',
        source_bank: tx.account ?? '',
        batchId: batchId || '',
        processingId: _processingId || '',
        created_at: undefined,
        updated_at: undefined
      }));
      console.log(`📊 Parsed ${transactions.length} rows from CSV`);
    } else {
      const xlsx = require('xlsx');
      const excel = require('./utils/excel-service');
      let workbook;
      try {
        workbook = xlsx.read(fileBuffer, { type: 'buffer' });
      } catch (err) {
        throw new Error(`Excel parsing failed: ${(err as Error).message}`);
      }
      transactions = excel.ExcelService.extractTransactions(workbook).map((tx: any, index: number) => ({
        ...tx,
        id: undefined,
        user_id: userId,
        fingerprint: this.generateFingerprint(tx, index),
        date: normalizeDate(tx.date) || '',
        type: tx.type === 'credit' ? 'credit' : 'debit',
        category: this.categorizeTransaction(tx.description ?? ''),
        vendor: tx.vendor ?? tx.merchant ?? '',
        bank_provided_id: tx.reference ?? '',
        source_bank: tx.account ?? '',
        batchId: batchId || '',
        processingId: _processingId || '',
        created_at: undefined,
        updated_at: undefined
      }));
      console.log(`📈 Parsed ${transactions.length} rows from Excel`);
    }
    return {
      transactions,
      accountInfo: {},
      summary: {
        totalCredits: transactions.filter(t => t.type === 'credit').reduce((a, b) => a + (b.amount || 0), 0),
        totalDebits: transactions.filter(t => t.type === 'debit').reduce((a, b) => a + (b.amount || 0), 0),
        netAmount: transactions.reduce((a, b) => a + (b.type === 'credit' ? b.amount : -b.amount), 0),
        transactionCount: transactions.length,
        dateRange: {
          start: transactions.length ? String(transactions[0]?.date || '') : '',
          end: transactions.length ? String(transactions[transactions.length - 1]?.date || '') : ''
        }
      },
      metadata: {
        documentType: 'bank_statement',
        source: 'CSV/Excel',
        processingTimestamp: new Date().toISOString(),
        confidence: 0.98
      }
    };
  }

  // Step 4: Validate and enhance extracted data
  public static async validateAndEnhanceData(
    extractedData: ExtractedData,
    userId: string,
    _processingId: string
  ): Promise<ExtractedData> {
    const enhancedTransactions = extractedData.transactions.map((tx: Transaction) => {
      const vendor = tx.vendor ?? this.extractVendorFromDescription(tx.description);
      return {
        ...tx,
        user_id: userId, // Set the actual user ID
        category: tx.category ?? this.categorizeTransaction(tx.description),
        source_bank: tx.source_bank ?? extractedData.accountInfo?.bankName ?? 'Unknown',
        ...(vendor && { vendor }) // Only include vendor if it has a value
      };
    });

    // Validate amounts and dates
    const validatedTransactions = enhancedTransactions.filter((tx: Transaction) => {
      return tx.amount > 0 && tx.date && tx.description;
    });

    return {
      ...extractedData,
      transactions: validatedTransactions
    };
  }

  // Step 5: Calculate confidence scores
  private static calculateConfidenceScores(data: ExtractedData): ConfidenceScore {
    const totalTransactions = data.transactions.length;
    if (totalTransactions === 0) {
      return {
        overall: 0.0,
        fields: {},
        textExtraction: 0.0,
        dataParsing: 0.0,
        validation: 0.0
      };
    }

    // Calculate confidence based on data completeness
    const completenessScores = data.transactions.map((tx: Transaction) => {
      let score = 0.0;
      if (tx.amount > 0) score += 0.3;
      if (tx.date) score += 0.2;
      if (tx.description && tx.description !== 'Unknown transaction') score += 0.2;
      if (tx.type) score += 0.1;
      if (tx.source_bank && tx.source_bank !== 'Unknown') score += 0.1;
      if (tx.category) score += 0.1;
      return score;
    });

    const avgCompleteness = completenessScores.reduce((a: number, b: number) => a + b, 0) / totalTransactions;

    return {
      overall: avgCompleteness,
      fields: {},
      textExtraction: 0.9, // High confidence for Gemini OCR
      dataParsing: avgCompleteness,
      validation: avgCompleteness
    };
  }

  // Step 6: Insert into Supabase
  private static async insertToSupabase(
    transactions: Transaction[],
    userId: string,
    processingId: string,
    batchId?: string
  ): Promise<SupabaseInsertResult> {
    try {
      // Attach batchId/processingId to each transaction
      const txs = transactions.map(tx => ({ ...tx, batchId, processingId }));
      const result = await insertTransactions(txs, userId);
      // Log the result
      console.log(`📊 Supabase insertion result:`, {
        inserted: result.insertedCount,
        duplicates: result.duplicateCount,
        errors: result.errorCount
      });
      return result;
    } catch (error) {
      console.error('Supabase insertion failed:', error);
      return {
        id: `error_${Date.now()}`,
        created_at: new Date().toISOString(),
        success: false,
        insertedCount: 0,
        duplicateCount: 0,
        errorCount: transactions.length,
        errors: [error instanceof Error ? error.message : 'Unknown error']
      };
    }
  }

  // Helper methods
  
  /**
   * Generates a unique fingerprint for transaction deduplication.
   * 
   * This function includes multiple distinguishing factors to prevent false duplicate detection:
   * - Time precision (date + time) to distinguish same-day transactions
   * - Session-specific identifiers (session_id, authorization_code, terminal_id)
   * - Device and location identifiers (device_id, ip_address, channel)
   * - Payment gateway and transaction context
   * 
   * Transactions with different session IDs, authorization codes, timestamps, or other
   * distinguishing factors will generate unique fingerprints, preventing false positive
   * duplicate detection while maintaining accurate duplicate identification for truly
   * identical transactions.
   */
  private static generateFingerprint(transaction: Transaction, _index: number): string {
    // Include distinguishing factors to prevent false duplicates
    // Include time with millisecond precision and session-specific identifiers
    const components = [
      transaction.date,
      transaction.time, // Include time to distinguish same-day transactions
      transaction.amount,
      transaction.description,
      transaction.bank_provided_id,
      transaction.session_id, // Include session ID to distinguish different sessions
      transaction.authorization_code, // Include authorization code
      transaction.terminal_id, // Include terminal ID
      transaction.device_id, // Include device ID
      transaction.ip_address, // Include IP address
      transaction.channel, // Include transaction channel
      transaction.payment_gateway // Include payment gateway
      // Removed index to ensure identical transactions generate same fingerprint
    ].filter(Boolean).join('|');
    
    // Use a longer hash to ensure distinguishing factors are captured
    // Use SHA-256 hash for better uniqueness and collision resistance
    const crypto = require('crypto');
    const hash = crypto.createHash('sha256').update(components).digest('hex');
    return hash.substring(0, 32); // Return first 32 characters for consistency
  }

  private static categorizeTransaction(description: string): string {
    const desc = description.toLowerCase();
    
    if (desc.includes('atm') || desc.includes('cash withdrawal')) return 'ATM';
    if (desc.includes('pos') || desc.includes('purchase')) return 'POS';
    if (desc.includes('transfer') || desc.includes('send')) return 'Transfer';
    if (desc.includes('deposit') || desc.includes('credit')) return 'Deposit';
    if (desc.includes('airtime') || desc.includes('data')) return 'Airtime/Data';
    if (desc.includes('bill') || desc.includes('payment')) return 'Bill Payment';
    if (desc.includes('salary') || desc.includes('income')) return 'Income';
    if (desc.includes('food') || desc.includes('restaurant')) return 'Food';
    if (desc.includes('transport') || desc.includes('uber') || desc.includes('taxi')) return 'Transport';
    if (desc.includes('shopping') || desc.includes('mall')) return 'Shopping';
    
    return 'Other';
  }

  private static extractVendorFromDescription(description: string): string | undefined {
    // Simple vendor extraction - can be enhanced with more sophisticated NLP
    // TODO: implement this with better NLP or ML

    
    // Common vendor patterns
    const vendorPatterns = [
      /pos\s+(\w+)/i,
      /atm\s+(\w+)/i,
      /(\w+)\s+pos/i,
      /(\w+)\s+atm/i
    ];

    for (const pattern of vendorPatterns) {
      const match = description.match(pattern);
      if (match && match[1]) {
        return match[1].toUpperCase();
      }
    }

    return undefined;
  }

  // Prompt templates
  private static getBasicAnalysisPrompt(): string {
    return `
    Parse this financial document text and extract structured transaction data.
    
    Extract the following information and return as JSON:
    {
      "transactions": [
        {
          "date": "YYYY-MM-DD",
          "time": "HH:MM:SS or null",
          "description": "transaction description",
          "amount": number,
          "type": "credit" | "debit",
          "currency": "currency code",
          "reference": "transaction reference or null",
          "merchant": "merchant name or null",
          "account": "account number or null",
          "balance": "account balance or null",
          "fees": "transaction fees or null",
          "channel": "transaction channel (ATM, POS, Mobile, Web, etc.)",
          "location": "transaction location or null",
          "device": "device information or null"
        }
      ],
      "accountInfo": {
        "accountNumber": "account number",
        "accountName": "account holder name",
        "accountType": "account type",
        "bankName": "bank name",
        "branch": "branch code or null"
      },
      "summary": {
        "totalCredits": number,
        "totalDebits": number,
        "netAmount": number,
        "transactionCount": number,
        "dateRange": {
          "start": "YYYY-MM-DD",
          "end": "YYYY-MM-DD"
        }
      }
    }
    `;
  }

  private static getAdvancedAnalysisPrompt(): string {
    return `
    Parse this financial document text and extract comprehensive structured transaction data.
    
    Extract the following information and return as JSON:
    {
      "transactions": [
        {
          "date": "YYYY-MM-DD",
          "time": "HH:MM:SS or null",
          "description": "transaction description",
          "amount": number,
          "type": "credit" | "debit",
          "currency": "currency code",
          "reference": "transaction reference or null",
          "merchant": "merchant name or null",
          "account": "account number or null",
          "balance": "account balance or null",
          "fees": "transaction fees or null",
          "channel": "transaction channel (ATM, POS, Mobile, Web, etc.)",
          "location": "transaction location or null",
          "device": "device information or null",
          "sender_name": "sender name or null",
          "receiver_name": "receiver name or null",
          "sender_account_number": "sender account number or null",
          "receiver_account_number": "receiver account number or null",
          "narration": "transaction narration or null",
          "authorization_code": "authorization code or null",
          "destination_bank": "destination bank or null",
          "balance_before": "balance before transaction or null",
          "balance_after": "balance after transaction or null",
          "exchange_rate": "exchange rate or null",
          "transaction_status": "transaction status or null",
          "payment_gateway": "payment gateway or null",
          "terminal_id": "terminal ID or null",
          "session_id": "session ID or null",
          "device_id": "device ID or null",
          "ip_address": "IP address or null",
          "geolocation": "geolocation or null"
        }
      ],
      "accountInfo": {
        "accountNumber": "account number",
        "accountName": "account holder name",
        "accountType": "account type",
        "bankName": "bank name",
        "branch": "branch code or null"
      },
      "summary": {
        "totalCredits": number,
        "totalDebits": number,
        "netAmount": number,
        "transactionCount": number,
        "dateRange": {
          "start": "YYYY-MM-DD",
          "end": "YYYY-MM-DD"
        }
      }
    }
    `;
  }

  // Logging methods
  private static async logProcessingStart(
    processingId: string,
    batchId: string,
    userId: string,
    fileName: string
  ): Promise<void> {
    const log: ProcessingLog = {
      id: uuidv4(),
      level: 'info',
      timestamp: new Date().toISOString(),
      message: 'Document processing started',
      data: {
        processingId,
        batchId,
        userId,
        fileName
      }
    };

    await SupabaseDocumentService.logProcessing(log);
  }

  private static async logProcessingComplete(
    processingId: string,
    batchId: string,
    userId: string,
    fileName: string,
    metadata: Record<string, any>
  ): Promise<void> {
    const log: ProcessingLog = {
      id: uuidv4(),
      level: 'info',
      timestamp: new Date().toISOString(),
      message: 'Document processing completed successfully',
      data: {
        processingId,
        batchId,
        userId,
        fileName,
        ...metadata
      }
    };

    await SupabaseDocumentService.logProcessing(log);
  }

  private static async logProcessingError(
    processingId: string,
    batchId: string,
    userId: string,
    fileName: string,
    error: string
  ): Promise<void> {
    const log: ProcessingLog = {
      id: uuidv4(),
      level: 'error',
      timestamp: new Date().toISOString(),
      message: 'Document processing failed',
      data: {
        processingId,
        batchId,
        userId,
        fileName,
        error
      }
    };

    await SupabaseDocumentService.logProcessing(log);
  }

  // Helper to log to document_processing_logs table (DRY)
  private static async logToSupabaseLogTable(params: Record<string, any>) {
    return supabaseClient.from('document_processing_logs').insert(params);
  }
} 

// Utility for date normalization (YYYY-MM-DD)
function normalizeDate(date: any): string {
  if (!date) return '';
  if (typeof date === 'string') {
    // Try to parse common formats
    const d = new Date(date);
    if (!isNaN(d.getTime())) return d.toISOString().split('T')[0] || '';
    // fallback: try to extract yyyy-mm-dd
    const match = date.match(/\d{4}-\d{2}-\d{2}/);
    if (match) return match[0] || '';
    return date || '';
  }
  if (typeof date === 'number') {
    // Excel serial date
    const excelEpoch = new Date(1899, 11, 30);
    const d = new Date(excelEpoch.getTime() + date * 86400000);
    return d.toISOString().split('T')[0] || '';
  }
  if (date instanceof Date) return date.toISOString().split('T')[0] || '';
  return String(date) || '';
}

// Export for test-driven validation
export const parseStructuredData = DocumentProcessor.parseStructuredData;
export const validateAndEnhanceData = DocumentProcessor.validateAndEnhanceData; 