import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { ProcessingLog, AuditTrail, BatchMetadata } from '../types';

export interface JobStatus {
  jobId: string;
  userId: string;
  fileName: string;
  status: 'queued' | 'processing' | 'completed' | 'failed' | 'cancelled';
  createdAt: Date;
  startedAt?: Date;
  completedAt?: Date;
  failedAt?: Date;
  cancelledAt?: Date;
  fileSize: number;
  mimeType: string;
  progress?: number;
  error?: string;
  result?: {
    transactionCount: number;
    processingTime: number;
    confidence: number;
  };
  options?: any;
}

export class SupabaseDocumentService {
  private static client: SupabaseClient;
  private static serviceClient: SupabaseClient;

  static initialize() {
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabasePublishableKey = process.env.SUPABASE_PUBLISHABLE_KEY;
    const supabaseSecretKey = process.env.SUPABASE_SECRET_KEY;

    if (!supabaseUrl || !supabasePublishableKey || !supabaseSecretKey) {
      throw new Error('Missing required Supabase environment variables');
    }

    // Client for general operations (with RLS)
    this.client = createClient(supabaseUrl, supabasePublishableKey);

    // Service role client for admin operations (bypasses RLS)
    this.serviceClient = createClient(supabaseUrl, supabaseSecretKey);

    console.log('✅ Supabase Document Service initialized');
  }

  // Verify user token
  static async verifyToken(token: string): Promise<{ user: any; error: any }> {
    try {
      const { data, error } = await this.client.auth.getUser(token);
      return { user: data.user, error };
    } catch (error) {
      return { user: null, error };
    }
  }

  // Logging methods - using PostgreSQL tables instead of Firestore collections
  static async logProcessing(log: ProcessingLog): Promise<void> {
    try {
      const { error } = await this.serviceClient
        .from('processing_logs')
        .insert({
          ...log,
          timestamp: new Date().toISOString(),
        });
      
      if (error) {
        console.error('Failed to log processing:', error);
      }
    } catch (error) {
      console.error('Failed to log processing:', error);
    }
  }

  static async logAuditTrail(audit: AuditTrail): Promise<void> {
    try {
      const { error } = await this.serviceClient
        .from('audit_trail')
        .insert({
          ...audit,
          timestamp: new Date().toISOString(),
        });
      
      if (error) {
        console.error('Failed to log audit trail:', error);
      }
    } catch (error) {
      console.error('Failed to log audit trail:', error);
    }
  }

  static async updateBatchStatus(batchId: string, status: BatchMetadata['status'], metadata?: Partial<BatchMetadata>): Promise<void> {
    try {
      const { error } = await this.serviceClient
        .from('batch_metadata')
        .update({
          status,
          ...metadata,
          updated_at: new Date().toISOString(),
        })
        .eq('batch_id', batchId);
      
      if (error) {
        console.error('Failed to update batch status:', error);
      }
    } catch (error) {
      console.error('Failed to update batch status:', error);
    }
  }

  static async createBatchMetadata(batch: BatchMetadata): Promise<void> {
    try {
      const { error } = await this.serviceClient
        .from('batch_metadata')
        .insert({
          batch_id: batch.batchId,
          ...batch,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        });
      
      if (error) {
        console.error('Failed to create batch metadata:', error);
      }
    } catch (error) {
      console.error('Failed to create batch metadata:', error);
    }
  }

  // File storage methods - using Supabase Storage
  static async uploadFile(fileBuffer: Buffer, fileName: string, userId: string): Promise<string> {
    try {
      const filePath = `documents/${userId}/${Date.now()}_${fileName}`;
      
      const { error } = await this.serviceClient.storage
        .from('documents')
        .upload(filePath, fileBuffer, {
          contentType: 'application/octet-stream',
          metadata: {
            userId,
            uploadedAt: new Date().toISOString(),
          },
        });

      if (error) {
        console.error('Failed to upload file:', error);
        throw new Error('File upload failed');
      }

      // Get public URL
      const { data: { publicUrl } } = this.serviceClient.storage
        .from('documents')
        .getPublicUrl(filePath);

      return publicUrl;
    } catch (error) {
      console.error('Failed to upload file:', error);
      throw new Error('File upload failed');
    }
  }

  // Error logging
  static async logError(error: Error, context: Record<string, any>): Promise<void> {
    try {
      const { error: insertError } = await this.serviceClient
        .from('error_logs')
        .insert({
          error_message: error.message,
          error_stack: error.stack,
          error_name: error.name,
          context,
          timestamp: new Date().toISOString(),
        });
      
      if (insertError) {
        console.error('Failed to log error:', insertError);
      }
    } catch (logError) {
      console.error('Failed to log error:', logError);
    }
  }

  // Performance monitoring
  static async logPerformance(operation: string, duration: number, metadata?: Record<string, any>): Promise<void> {
    try {
      const { error } = await this.serviceClient
        .from('performance_logs')
        .insert({
          operation,
          duration,
          metadata,
          timestamp: new Date().toISOString(),
        });
      
      if (error) {
        console.error('Failed to log performance:', error);
      }
    } catch (error) {
      console.error('Failed to log performance:', error);
    }
  }

  // Get batch status
  static async getBatchStatus(batchId: string): Promise<BatchMetadata | null> {
    try {
      const { data, error } = await this.serviceClient
        .from('batch_metadata')
        .select('*')
        .eq('batch_id', batchId)
        .single();
      
      if (error || !data) {
        console.error('Failed to get batch status:', error);
        return null;
      }
      
      return { batchId, ...data } as BatchMetadata;
    } catch (error) {
      console.error('Failed to get batch status:', error);
      return null;
    }
  }

  // Job status methods
  static async createJobStatus(job: JobStatus): Promise<void> {
    try {
      const { error } = await this.serviceClient
        .from('job_status')
        .insert({
          job_id: job.jobId,
          user_id: job.userId,
          file_name: job.fileName,
          status: job.status,
          created_at: job.createdAt.toISOString(),
          file_size: job.fileSize,
          mime_type: job.mimeType,
          progress: job.progress,
          error: job.error,
          result: job.result,
          options: job.options,
        });
      
      if (error) {
        console.error('Failed to create job status:', error);
      }
    } catch (error) {
      console.error('Failed to create job status:', error);
    }
  }

  static async updateJobStatus(jobId: string, updates: Partial<JobStatus>): Promise<void> {
    try {
      const updateData: any = {};
      
      if (updates.status) updateData.status = updates.status;
      if (updates.startedAt) updateData.started_at = updates.startedAt.toISOString();
      if (updates.completedAt) updateData.completed_at = updates.completedAt.toISOString();
      if (updates.failedAt) updateData.failed_at = updates.failedAt.toISOString();
      if (updates.cancelledAt) updateData.cancelled_at = updates.cancelledAt.toISOString();
      if (updates.progress !== undefined) updateData.progress = updates.progress;
      if (updates.error) updateData.error = updates.error;
      if (updates.result) updateData.result = updates.result;
      
      updateData.updated_at = new Date().toISOString();

      const { error } = await this.serviceClient
        .from('job_status')
        .update(updateData)
        .eq('job_id', jobId);
      
      if (error) {
        console.error('Failed to update job status:', error);
      }
    } catch (error) {
      console.error('Failed to update job status:', error);
    }
  }

  static async getJobStatus(jobId: string): Promise<JobStatus | null> {
    try {
      const { data, error } = await this.serviceClient
        .from('job_status')
        .select('*')
        .eq('job_id', jobId)
        .single();
      
      if (error || !data) {
        return null;
      }
      
      return {
        jobId: data.job_id,
        userId: data.user_id,
        fileName: data.file_name,
        status: data.status,
        createdAt: new Date(data.created_at),
        startedAt: data.started_at ? new Date(data.started_at) : undefined,
        completedAt: data.completed_at ? new Date(data.completed_at) : undefined,
        failedAt: data.failed_at ? new Date(data.failed_at) : undefined,
        cancelledAt: data.cancelled_at ? new Date(data.cancelled_at) : undefined,
        fileSize: data.file_size,
        mimeType: data.mime_type,
        progress: data.progress,
        error: data.error,
        result: data.result,
        options: data.options,
      } as JobStatus;
    } catch (error) {
      console.error('Failed to get job status:', error);
      return null;
    }
  }

  static async getUserJobs(userId: string, limit: number = 20): Promise<JobStatus[]> {
    try {
      const { data, error } = await this.serviceClient
        .from('job_status')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Failed to get user jobs:', error);
        return [];
      }

      return (data || []).map(item => ({
        jobId: item.job_id,
        userId: item.user_id,
        fileName: item.file_name,
        status: item.status,
        createdAt: new Date(item.created_at),
        startedAt: item.started_at ? new Date(item.started_at) : undefined,
        completedAt: item.completed_at ? new Date(item.completed_at) : undefined,
        failedAt: item.failed_at ? new Date(item.failed_at) : undefined,
        cancelledAt: item.cancelled_at ? new Date(item.cancelled_at) : undefined,
        fileSize: item.file_size,
        mimeType: item.mime_type,
        progress: item.progress,
        error: item.error,
        result: item.result,
        options: item.options,
      })) as JobStatus[];
    } catch (error) {
      console.error('Failed to get user jobs:', error);
      return [];
    }
  }

  // Get processing history for a user
  static async getProcessingHistory(userId: string, limit: number = 50): Promise<ProcessingLog[]> {
    try {
      const { data, error } = await this.serviceClient
        .from('processing_logs')
        .select('*')
        .eq('user_id', userId)
        .order('timestamp', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Failed to get processing history:', error);
        return [];
      }

      return (data || []).map(item => ({
        id: item.id,
        level: item.level || 'info',
        timestamp: item.timestamp,
        message: item.message || '',
        data: item.metadata || {}
      })) as ProcessingLog[];
    } catch (error) {
      console.error('Failed to get processing history:', error);
      return [];
    }
  }

  // Cleanup old jobs
  static async cleanupOldJobs(cutoffDate: Date): Promise<void> {
    try {
      // Delete old job status records
      const { error: jobError } = await this.serviceClient
        .from('job_status')
        .delete()
        .lt('created_at', cutoffDate.toISOString());

      if (jobError) {
        console.error('Failed to cleanup old job status records:', jobError);
      }

      // Delete old processing logs
      const { error: logError } = await this.serviceClient
        .from('processing_logs')
        .delete()
        .lt('timestamp', cutoffDate.toISOString());

      if (logError) {
        console.error('Failed to cleanup old processing logs:', logError);
      }

      // Delete old batch metadata
      const { error: batchError } = await this.serviceClient
        .from('batch_metadata')
        .delete()
        .lt('created_at', cutoffDate.toISOString());

      if (batchError) {
        console.error('Failed to cleanup old batch metadata:', batchError);
      }

      console.log(`✅ Cleaned up old records before ${cutoffDate.toISOString()}`);
    } catch (error) {
      console.error('Failed to cleanup old jobs:', error);
    }
  }
}
