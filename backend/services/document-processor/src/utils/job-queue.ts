import Queue from 'bull';
import { DocumentProcessor } from '../document-processor';
import { SupabaseDocumentService } from './supabase-service';
import { ProcessingOptions } from '../types';

// To use Upstash Redis with ioredis (Redis protocol), set the following environment variable in your .env file:
// REDIS_URL="rediss://default:password@host:port"

// Upstash Redis configuration for Bull (ioredis)
const redisUrl = process.env.REDIS_URL;

if (!redisUrl) {
  throw new Error('Redis URL is not set. Please set REDIS_URL in your .env file.');
}

// Create document processing queue with Upstash
const documentQueue = new Queue('document-processing', redisUrl, {
  defaultJobOptions: {
    attempts: 3,
    backoff: {
      type: 'exponential',
      delay: 2000,
    },
    removeOnComplete: 100,
    removeOnFail: 50,
  },
});

// Job processor
documentQueue.process(async (job) => {
  const {
    fileBuffer,
    mimeType,
    userId,
    fileName,
    options,
    jobId
  } = job.data;

  console.log(`🔄 Processing job ${jobId} for user ${userId}: ${fileName}`);

  try {
    // Update job status to processing
    await SupabaseDocumentService.updateJobStatus(jobId, {
      status: 'processing',
      startedAt: new Date(),
      progress: 0
    });

    // Process the document
    const result = await DocumentProcessor.processDocument(
      fileBuffer,
      mimeType,
      userId,
      fileName,
      options
    );

    if (result.success) {
      // Update job status to completed
      await SupabaseDocumentService.updateJobStatus(jobId, {
        status: 'completed',
        completedAt: new Date(),
        progress: 100,
        result: {
          transactionCount: result.extractedData?.transactions?.length || 0,
          processingTime: result.processingTime || 0,
          confidence: result.confidence?.overall || 0
        }
      });

      console.log(`✅ Job ${jobId} completed successfully`);
      return result;
    } else {
      throw new Error(result.error || 'Document processing failed');
    }

  } catch (error) {
    console.error(`❌ Job ${jobId} failed:`, error);

    // Update job status to failed
    await SupabaseDocumentService.updateJobStatus(jobId, {
      status: 'failed',
      failedAt: new Date(),
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    throw error;
  }
});

// Job progress tracking
documentQueue.on('progress', async (job, progress) => {
  const { jobId } = job.data;
  await SupabaseDocumentService.updateJobStatus(jobId, {
    progress: progress
  });
});

// Job completion tracking
documentQueue.on('completed', async (job, _result) => {
  const { jobId, userId } = job.data;
  console.log(`🎉 Job ${jobId} completed for user ${userId}`);
});

// Job failure tracking
documentQueue.on('failed', async (job, err) => {
  const { jobId, userId } = job.data;
  console.error(`💥 Job ${jobId} failed for user ${userId}:`, err.message);
});

export class JobQueueService {
  /**
   * Add a document processing job to the queue
   */
  static async addDocumentJob(
    fileBuffer: Buffer,
    mimeType: string,
    userId: string,
    fileName: string,
    options: ProcessingOptions = {}
  ): Promise<string> {
    const jobId = `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Create initial job status
    await SupabaseDocumentService.createJobStatus({
      jobId,
      userId,
      fileName,
      status: 'queued',
      createdAt: new Date(),
      fileSize: fileBuffer.length,
      mimeType,
      options
    });

    // Add job to queue
    const job = await documentQueue.add({
      fileBuffer,
      mimeType,
      userId,
      fileName,
      options,
      jobId
    });

    console.log(`📝 Job ${jobId} added to queue (job.id: ${job.id})`);
    return jobId;
  }

  /**
   * Get job status
   */
  static async getJobStatus(jobId: string): Promise<any> {
    return await SupabaseDocumentService.getJobStatus(jobId);
  }

  /**
   * Get user's job history
   */
  static async getUserJobs(userId: string, limit: number = 20): Promise<any[]> {
    return await SupabaseDocumentService.getUserJobs(userId, limit);
  }

  /**
   * Cancel a job
   */
  static async cancelJob(jobId: string): Promise<boolean> {
    try {
      // Find the job in the queue
      const jobs = await documentQueue.getJobs(['waiting', 'active']);
      const job = jobs.find(j => j.data.jobId === jobId);
      
      if (job) {
        await job.remove();
        await SupabaseDocumentService.updateJobStatus(jobId, {
          status: 'cancelled',
          cancelledAt: new Date()
        });
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Error cancelling job:', error);
      return false;
    }
  }

  /**
   * Get queue statistics
   */
  static async getQueueStats(): Promise<any> {
    const [waiting, active, completed, failed] = await Promise.all([
      documentQueue.getWaiting(),
      documentQueue.getActive(),
      documentQueue.getCompleted(),
      documentQueue.getFailed()
    ]);

    return {
      waiting: waiting.length,
      active: active.length,
      completed: completed.length,
      failed: failed.length,
      total: waiting.length + active.length + completed.length + failed.length
    };
  }

  /**
   * Clean up old jobs
   */
  static async cleanupOldJobs(daysOld: number = 7): Promise<void> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);

    await SupabaseDocumentService.cleanupOldJobs(cutoffDate);
  }
}

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('SIGTERM received, closing job queue...');
  await documentQueue.close();
  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('SIGINT received, closing job queue...');
  await documentQueue.close();
  process.exit(0);
});

export default JobQueueService; 