import { Transaction, SupabaseInsertResult, TransactionStats } from '../types';

export declare function upsertCategory(userId: string, name: string, color?: string, icon?: string): Promise<void>;
export declare function upsertVendor(userId: string, name: string, categoryId?: string, normalizedName?: string): Promise<void>;
export declare function addTransactions(transactions: Transaction[]): Promise<Transaction[]>;
export declare function getTransactions(userId: string): Promise<Transaction[]>;
export declare function updateTransaction(id: string, updates: Partial<Transaction>): Promise<Transaction>;
export declare function deleteTransaction(id: string): Promise<void>;
export declare function insertTransactions(transactions: Transaction[], userId: string): Promise<SupabaseInsertResult>;
export declare function getTransactionStats(userId: string, startDate?: string, endDate?: string): Promise<TransactionStats>;
export declare function checkTransactionExists(fingerprint: string, userId: string): Promise<boolean>;
export declare function autoCategorizeTransactions(transactionIds: string[], userId: string): Promise<Transaction[]>;
export declare function getSpendingByCategory(userId: string, startDate?: string, endDate?: string): Promise<{
        category: string;
        amount: unknown;
    }[]>;