import { supabaseClient } from './supabaseClient';
import { Transaction, SupabaseInsertResult, TransactionStats } from '../types';

// Re-export supabaseClient for use by other modules
export { supabaseClient };

  // Add or update a category for a user if not present
export async function upsertCategory(userId: string, name: string, color?: string, icon?: string) {
    if (!name) return;
  await supabaseClient.from('categories').upsert({
      user_id: userId,
      name,
      color: color || '#3B82F6',
      icon: icon || null
    }, { onConflict: 'user_id,name' });
  }

  // Add or update a vendor for a user if not present
export async function upsertVendor(userId: string, name: string, categoryId?: string, normalizedName?: string) {
    if (!name) return;
  await supabaseClient.from('vendors').upsert({
      user_id: userId,
      name,
      normalized_name: (normalizedName || name).toLowerCase().trim(),
      category_id: categoryId || null
    }, { onConflict: 'user_id,normalized_name' });
  }

  // Alias for insertTransactions to match controller expectations
export async function addTransactions(transactions: Transaction[]): Promise<Transaction[]> {
    if (transactions.length === 0) return [];
    const userId = transactions[0].user_id;
  const result = await insertTransactions(transactions, userId);
    if (!result.success) {
      throw new Error(`Failed to insert transactions: ${result.errors?.join(', ') || 'Unknown error'}`);
    }
    return transactions.map((tx, index) => ({
      ...tx,
      id: result.transactionIds?.[index] || `temp_${Date.now()}_${index}`
    }));
  }

  // Get all transactions for a user
export async function getTransactions(userId: string): Promise<Transaction[]> {
    try {
    const { data, error } = await supabaseClient
        .from('transactions')
        .select('*')
        .eq('user_id', userId)
        .order('date', { ascending: false });
      if (error) {
        console.error('Error fetching transactions:', error);
        throw error;
      }
      return data || [];
    } catch (error) {
      console.error('Failed to fetch transactions:', error);
      throw error;
    }
  }

  // Update a transaction
export async function updateTransaction(id: string, updates: Partial<Transaction>): Promise<Transaction> {
    try {
    const { data, error } = await supabaseClient
        .from('transactions')
        .update(updates)
        .eq('id', id)
        .select()
        .single();
      if (error) {
        console.error('Error updating transaction:', error);
        throw error;
      }
      return data;
    } catch (error) {
      console.error('Failed to update transaction:', error);
      throw error;
    }
  }

  // Delete a transaction
export async function deleteTransaction(id: string): Promise<void> {
    try {
    const { error } = await supabaseClient
        .from('transactions')
        .delete()
        .eq('id', id);
      if (error) {
        console.error('Error deleting transaction:', error);
        throw error;
      }
    } catch (error) {
      console.error('Failed to delete transaction:', error);
      throw error;
    }
  }

  // When inserting transactions, also upsert categories/vendors if present
export async function insertTransactions(
    transactions: Transaction[],
    userId: string
  ): Promise<SupabaseInsertResult> {
    for (const tx of transactions) {
      if (tx.category) {
      await upsertCategory(userId, tx.category);
      }
      if (tx.vendor) {
      await upsertVendor(userId, tx.vendor);
      }
    }
    const batchSize = 100;
    let insertedCount = 0;
    let duplicateCount = 0;
    let errorCount = 0;
    const errors: string[] = [];
    let transactionIds: string[] = [];
    for (let i = 0; i < transactions.length; i += batchSize) {
      const batch = transactions.slice(i, i + batchSize);
      try {
      const { data, error } = await supabaseClient
          .from('transactions')
          .upsert(batch, {
            onConflict: 'user_id,fingerprint',
            ignoreDuplicates: true
          })
          .select('id');
        if (error) throw error;
        insertedCount += data?.length || 0;
        duplicateCount += batch.length - (data?.length || 0);
        transactionIds.push(...(data?.map((tx: any) => tx.id) || []));
      } catch (error) {
        errorCount += batch.length;
        errors.push(error instanceof Error ? error.message : 'Unknown error');
      }
    }
    return {
      id: `batch_${Date.now()}`,
      created_at: new Date().toISOString(),
      success: errorCount === 0,
      insertedCount,
      duplicateCount,
      errorCount,
      errors,
      transactionIds
    };
  }

export async function getTransactionStats(userId: string, startDate?: string, endDate?: string): Promise<TransactionStats> {
    try {
    let query = supabaseClient
        .from('transactions')
        .select('amount, type, category')
        .eq('user_id', userId);
      if (startDate && endDate) {
        query = query.gte('date', startDate).lte('date', endDate);
      }
      const { data, error } = await query;
      if (error) {
        console.error('Error getting transaction stats:', error);
        return {
          totalIncome: 0,
          totalExpenses: 0,
          netCashFlow: 0,
          transactionCount: 0,
          categoryBreakdown: {},
        };
      }
      const transactions = data || [];
      const totalIncome = transactions
        .filter((t: any) => t.type === 'credit')
        .reduce((sum: number, t: any) => sum + (t.amount || 0), 0);
      const totalExpenses = transactions
        .filter((t: any) => t.type === 'debit')
        .reduce((sum: number, t: any) => sum + (t.amount || 0), 0);
      const categoryBreakdown = transactions.reduce((acc: Record<string, number>, tx: any) => {
        const category = tx.category || 'Uncategorized';
        acc[category] = (acc[category] || 0) + (tx.amount || 0);
        return acc;
      }, {});
      return {
        totalIncome,
        totalExpenses,
        netCashFlow: totalIncome - totalExpenses,
        transactionCount: transactions.length,
        categoryBreakdown,
      };
    } catch (error) {
      console.error('Error getting transaction stats:', error);
      return {
        totalIncome: 0,
        totalExpenses: 0,
        netCashFlow: 0,
        transactionCount: 0,
        categoryBreakdown: {},
      };
    }
  }

export async function checkTransactionExists(fingerprint: string, userId: string): Promise<boolean> {
    try {
    const { data, error } = await supabaseClient
        .from('transactions')
        .select('id')
        .eq('fingerprint', fingerprint)
        .eq('user_id', userId)
        .limit(1);
      if (error) {
        console.error('Error checking transaction existence:', error);
        return false;
      }
      return (data?.length || 0) > 0;
    } catch (error) {
      console.error('Error checking transaction existence:', error);
      return false;
    }
  }

export async function autoCategorizeTransactions(transactionIds: string[], userId: string): Promise<Transaction[]> {
    try {
    const { data, error } = await supabaseClient
        .from('transactions')
        .update({ category: 'Auto' })
        .in('id', transactionIds)
        .eq('user_id', userId)
        .select();
      if (error) {
        console.error('Error auto-categorizing transactions:', error);
        throw error;
      }
      return data || [];
    } catch (error) {
      console.error('Failed to auto-categorize transactions:', error);
      throw error;
    }
  }

export async function getSpendingByCategory(
    userId: string,
    startDate?: string,
    endDate?: string
  ) {
  let query = supabaseClient
      .from('transactions')
      .select('category, amount, type')
      .eq('user_id', userId)
      .eq('type', 'debit');
    if (startDate) query = query.gte('date', startDate);
    if (endDate) query = query.lte('date', endDate);
    const { data, error } = await query;
    if (error) throw error;
    const categoryTotals = (data || []).reduce((acc: Record<string, number>, transaction: any) => {
      const category = transaction.category || 'Uncategorized';
      acc[category] = (acc[category] || 0) + transaction.amount;
      return acc;
    }, {} as Record<string, number>);
    return Object.entries(categoryTotals)
      .map(([category, amount]) => ({ category, amount }))
      .sort((a, b) => (b as { amount: number }).amount - (a as { amount: number }).amount);
} 