import { GoogleAuth } from 'google-auth-library';

/**
 * Authentication utilities for Cloud Run services
 */

/**
 * Get a Google Cloud Run ID token for service-to-service authentication
 * @param audience - The target service URL
 * @returns Promise<string> - The ID token
 */
export async function getCloudRunIdToken(audience: string): Promise<string> {
  try {
    const auth = new GoogleAuth();
    const client = await auth.getIdTokenClient(audience);
    
    // @ts-ignore - getRequestHeaders() returns Authorization header
    const { Authorization } = await client.getRequestHeaders();
    
    // The header is in the form 'Bearer <token>'
    return Authorization?.split(' ')[1] || '';
  } catch (error) {
    console.error('Failed to get Cloud Run ID token:', error);
    throw new Error(`Failed to authenticate with Cloud Run service: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}
