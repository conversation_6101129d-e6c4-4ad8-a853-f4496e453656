import { createClient, SupabaseClient } from '@supabase/supabase-js';

let _supabaseClient: SupabaseClient | null = null;

export function getSupabaseClient(): SupabaseClient {
  if (!_supabaseClient) {
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !supabaseKey) {
      throw new Error('SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY environment variables are required');
    }

    _supabaseClient = createClient(supabaseUrl, supabaseKey);
  }

  return _supabaseClient;
}

// For backward compatibility
export const supabaseClient = new Proxy({} as SupabaseClient, {
  get(_unused, prop) {
    return getSupabaseClient()[prop as keyof SupabaseClient];
  }
});