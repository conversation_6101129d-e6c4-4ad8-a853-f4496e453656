{"name": "finscope-backend", "version": "1.0.0", "description": "FinScope Backend API", "main": "dist/src/index.js", "scripts": {"clean": "rm -rf dist && find src -name '*.js' -delete && find src -name '*.d.ts' -delete && find src -name '*.map' -delete", "prebuild": "npm run clean", "start": "node dist/src/index.js", "dev": "nodemon src/index.ts", "build": "tsc", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["finscope", "finance", "ai", "api"], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@google/genai": "^1.6.0", "@supabase/supabase-js": "^2.50.3", "axios": "^1.10.0", "bull": "^4.16.5", "cors": "^2.8.5", "csv-parse": "^6.0.0", "dotenv": "^16.4.5", "express": "^4.21.2", "google-auth-library": "^9.0.0", "helmet": "^8.0.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "pdf-parse": "^1.1.1", "pdf2pic": "^3.2.0", "sharp": "^0.34.3", "tesseract.js": "^6.0.1", "uuid": "^11.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/bull": "^3.15.9", "@types/cors": "^2.8.17", "@types/csv-parse": "^1.1.12", "@types/express": "^4.17.23", "@types/morgan": "^1.9.10", "@types/multer": "^1.4.12", "@types/node": "^22.16.3", "@types/pdf-parse": "^1.1.5", "@types/uuid": "^10.0.0", "@types/xlsx": "^0.0.35", "nodemon": "^3.1.0", "typescript": "~5.7.2"}}