/**
 * Database Connection Pool Configuration
 * 
 * This module provides optimized database connection pooling
 * for Supabase PostgreSQL connections.
 */

import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { Pool, PoolClient } from 'pg';

interface DatabaseConfig {
  supabaseUrl: string;
  supabaseServiceKey: string;
  poolConfig?: {
    max?: number;
    min?: number;
    idleTimeoutMillis?: number;
    connectionTimeoutMillis?: number;
    maxUses?: number;
  };
}

interface PerformanceMetrics {
  totalQueries: number;
  totalDuration: number;
  slowQueries: number;
  errors: number;
  activeConnections: number;
  poolSize: number;
}

class DatabasePool {
  private static instance: DatabasePool;
  private supabaseClient: SupabaseClient;
  private pgPool: Pool | null = null;
  private metrics: PerformanceMetrics;
  private config: DatabaseConfig;

  private constructor(config: DatabaseConfig) {
    this.config = config;
    this.metrics = {
      totalQueries: 0,
      totalDuration: 0,
      slowQueries: 0,
      errors: 0,
      activeConnections: 0,
      poolSize: 0
    };

    // Initialize Supabase client
    this.supabaseClient = createClient(
      config.supabaseUrl,
      config.supabaseServiceKey,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        },
        db: {
          schema: 'public'
        }
      }
    );

    // Initialize PostgreSQL pool if direct connection is needed
    this.initializePostgreSQLPool();
  }

  public static getInstance(config?: DatabaseConfig): DatabasePool {
    if (!DatabasePool.instance) {
      if (!config) {
        throw new Error('Database configuration required for first initialization');
      }
      DatabasePool.instance = new DatabasePool(config);
    }
    return DatabasePool.instance;
  }

  private initializePostgreSQLPool(): void {
    try {
      // Extract connection details from Supabase URL
      const url = new URL(this.config.supabaseUrl);
      const host = url.hostname;
      const port = parseInt(url.port) || 5432;
      
      // Note: Direct PostgreSQL connection requires database credentials
      // This is optional and mainly for advanced use cases
      const poolConfig = {
        host,
        port,
        database: 'postgres',
        // These would need to be provided separately for direct connections
        // user: process.env.POSTGRES_USER,
        // password: process.env.POSTGRES_PASSWORD,
        max: this.config.poolConfig?.max || 20,
        min: this.config.poolConfig?.min || 5,
        idleTimeoutMillis: this.config.poolConfig?.idleTimeoutMillis || 30000,
        connectionTimeoutMillis: this.config.poolConfig?.connectionTimeoutMillis || 10000,
        maxUses: this.config.poolConfig?.maxUses || 7500,
        ssl: {
          rejectUnauthorized: false
        }
      };

      // Only create pool if we have credentials
      if (process.env.POSTGRES_USER && process.env.POSTGRES_PASSWORD) {
        this.pgPool = new Pool({
          ...poolConfig,
          user: process.env.POSTGRES_USER,
          password: process.env.POSTGRES_PASSWORD
        });

        this.pgPool.on('connect', () => {
          this.metrics.activeConnections++;
        });

        this.pgPool.on('remove', () => {
          this.metrics.activeConnections--;
        });

        this.pgPool.on('error', (err: Error) => {
          console.error('PostgreSQL pool error:', err);
          this.metrics.errors++;
        });

        console.log('✅ PostgreSQL connection pool initialized');
      } else {
        console.log('ℹ️  PostgreSQL pool not initialized - using Supabase client only');
      }
    } catch (error) {
      console.error('Failed to initialize PostgreSQL pool:', error);
    }
  }

  // Get Supabase client (recommended approach)
  public getSupabaseClient(): SupabaseClient {
    return this.supabaseClient;
  }

  // Execute query with performance tracking
  public async executeQuery<T>(
    query: string,
    params?: any[],
    options?: { timeout?: number; trackPerformance?: boolean }
  ): Promise<T> {
    const startTime = Date.now();
    const trackPerformance = options?.trackPerformance !== false;

    try {
      if (trackPerformance) {
        this.metrics.totalQueries++;
      }

      // Use Supabase client for queries
      const { data, error } = await this.supabaseClient.rpc('execute_sql', {
        sql_query: query,
        params: params || []
      });

      if (error) {
        throw error;
      }

      const duration = Date.now() - startTime;

      if (trackPerformance) {
        this.metrics.totalDuration += duration;
        
        // Track slow queries (>1000ms)
        if (duration > 1000) {
          this.metrics.slowQueries++;
          console.warn(`Slow query detected (${duration}ms):`, query.substring(0, 100));
        }
      }

      return data as T;
    } catch (error) {
      if (trackPerformance) {
        this.metrics.errors++;
      }
      console.error('Query execution failed:', error);
      throw error;
    }
  }

  // Execute transaction with rollback support
  public async executeTransaction<T>(
    operations: ((client: SupabaseClient) => Promise<T>)[]
  ): Promise<T[]> {
    const results: T[] = [];
    
    try {
      // Note: Supabase doesn't support explicit transactions in the same way as raw PostgreSQL
      // This is a simplified implementation
      for (const operation of operations) {
        const result = await operation(this.supabaseClient);
        results.push(result);
      }
      
      return results;
    } catch (error) {
      console.error('Transaction failed:', error);
      throw error;
    }
  }

  // Get direct PostgreSQL client (for advanced use cases)
  public async getPostgreSQLClient(): Promise<PoolClient | null> {
    if (!this.pgPool) {
      return null;
    }

    try {
      return await this.pgPool.connect();
    } catch (error) {
      console.error('Failed to get PostgreSQL client:', error);
      this.metrics.errors++;
      return null;
    }
  }

  // Performance monitoring methods
  public getMetrics(): PerformanceMetrics {
    return {
      ...this.metrics,
      poolSize: this.pgPool?.totalCount || 0
    };
  }

  public getAverageQueryTime(): number {
    return this.metrics.totalQueries > 0 
      ? this.metrics.totalDuration / this.metrics.totalQueries 
      : 0;
  }

  public resetMetrics(): void {
    this.metrics = {
      totalQueries: 0,
      totalDuration: 0,
      slowQueries: 0,
      errors: 0,
      activeConnections: this.metrics.activeConnections,
      poolSize: this.metrics.poolSize
    };
  }

  // Health check
  public async healthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    metrics: PerformanceMetrics;
    details: any;
  }> {
    try {
      const startTime = Date.now();
      
      // Test Supabase connection
      const { error } = await this.supabaseClient
        .from('transactions')
        .select('count')
        .limit(1);

      const responseTime = Date.now() - startTime;

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      const metrics = this.getMetrics();
      const avgQueryTime = this.getAverageQueryTime();

      let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';

      // Determine health status
      if (responseTime > 5000 || avgQueryTime > 2000 || metrics.errors > 10) {
        status = 'unhealthy';
      } else if (responseTime > 1000 || avgQueryTime > 500 || metrics.errors > 5) {
        status = 'degraded';
      }

      return {
        status,
        metrics,
        details: {
          responseTime,
          avgQueryTime,
          supabaseConnected: true,
          postgresPoolConnected: !!this.pgPool
        }
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        metrics: this.getMetrics(),
        details: {
          error: error instanceof Error ? error.message : String(error),
          supabaseConnected: false,
          postgresPoolConnected: !!this.pgPool
        }
      };
    }
  }

  // Cleanup and close connections
  public async close(): Promise<void> {
    try {
      if (this.pgPool) {
        await this.pgPool.end();
        console.log('PostgreSQL pool closed');
      }
      
      // Supabase client doesn't need explicit closing
      console.log('Database connections closed');
    } catch (error) {
      console.error('Error closing database connections:', error);
    }
  }
}

// Factory function for easy initialization
export function createDatabasePool(config?: {
  maxConnections?: number;
  minConnections?: number;
  idleTimeout?: number;
  connectionTimeout?: number;
}): DatabasePool {
  const supabaseUrl = process.env.SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseServiceKey) {
    throw new Error('Missing required Supabase environment variables');
  }

  const dbConfig: DatabaseConfig = {
    supabaseUrl,
    supabaseServiceKey,
    poolConfig: {
      max: config?.maxConnections || 20,
      min: config?.minConnections || 5,
      idleTimeoutMillis: config?.idleTimeout || 30000,
      connectionTimeoutMillis: config?.connectionTimeout || 10000
    }
  };

  return DatabasePool.getInstance(dbConfig);
}

// Export types and main class
export { DatabasePool };
export type { DatabaseConfig, PerformanceMetrics };
export default DatabasePool;
