import { createClient, SupabaseClient } from '@supabase/supabase-js';

// Supabase configuration for backend services
class SupabaseConfig {
  private static instance: SupabaseConfig;
  private client: SupabaseClient | null = null;
  private serviceClient: SupabaseClient | null = null;

  private constructor() {}

  public static getInstance(): SupabaseConfig {
    if (!SupabaseConfig.instance) {
      SupabaseConfig.instance = new SupabaseConfig();
    }
    return SupabaseConfig.instance;
  }

  // Initialize Supabase clients
  public initialize(): void {
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabasePublishableKey = process.env.SUPABASE_PUBLISHABLE_KEY;
    const supabaseSecretKey = process.env.SUPABASE_SECRET_KEY;

    if (!supabaseUrl) {
      throw new Error('SUPABASE_URL environment variable is required');
    }

    if (!supabasePublishableKey) {
      throw new Error('SUPABASE_PUBLISHABLE_KEY environment variable is required');
    }

    if (!supabaseSecretKey) {
      throw new Error('SUPABASE_SECRET_KEY environment variable is required');
    }

    // Client for general operations (with RLS)
    this.client = createClient(supabaseUrl, supabasePublishableKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });

    // Service role client for admin operations (bypasses RLS)
    this.serviceClient = createClient(supabaseUrl, supabaseSecretKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });

    console.log('✅ Supabase clients initialized successfully');
  }

  // Get client for general operations
  public getClient(): SupabaseClient {
    if (!this.client) {
      throw new Error('Supabase client not initialized. Call initialize() first.');
    }
    return this.client;
  }

  // Get service role client for admin operations
  public getServiceClient(): SupabaseClient {
    if (!this.serviceClient) {
      throw new Error('Supabase service client not initialized. Call initialize() first.');
    }
    return this.serviceClient;
  }

  // Verify user token and get user info
  public async verifyToken(token: string): Promise<{ user: any; error: any }> {
    try {
      const { data, error } = await this.getClient().auth.getUser(token);
      return { user: data.user, error };
    } catch (error) {
      return { user: null, error };
    }
  }

  // Get user by ID (admin operation)
  public async getUserById(userId: string): Promise<{ user: any; error: any }> {
    try {
      const { data, error } = await this.getServiceClient().auth.admin.getUserById(userId);
      return { user: data.user, error };
    } catch (error) {
      return { user: null, error };
    }
  }
}

// Export singleton instance
export const supabaseConfig = SupabaseConfig.getInstance();

// Export convenience functions
export const initializeSupabase = () => supabaseConfig.initialize();
export const getSupabaseClient = () => supabaseConfig.getClient();
export const getSupabaseServiceClient = () => supabaseConfig.getServiceClient();
export const verifySupabaseToken = (token: string) => supabaseConfig.verifyToken(token);
export const getSupabaseUserById = (userId: string) => supabaseConfig.getUserById(userId);
