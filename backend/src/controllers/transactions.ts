import { getTransactions, updateTransaction, deleteTransaction, getTransactionStats, autoCategorizeTransactions } from '../../services/document-processor/src/utils/supabase';
import { AuthRequest, ApiResponse, Transaction, TransactionStats } from '../../services/document-processor/src/types';

export class TransactionsController {
  static async getTransactions(req: AuthRequest, res: any) {
    try {
      const userId = req.user!.uid;
      const transactions = await getTransactions(userId);

      const response: ApiResponse<Transaction[]> = {
        success: true,
        data: transactions,
        message: `Retrieved ${transactions.length} transactions`
      };

      res.json(response);
    } catch (error) {
      console.error('Get transactions error:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to retrieve transactions'
      });
    }
  }

  static async updateTransaction(req: AuthRequest, res: any) {
    try {
      const { id } = req.params as { id: string };
      const updates = req.body as Partial<Transaction>;


      if (!id) {
        return res.status(400).json({
          success: false,
          error: 'Transaction ID is required'
        });
      }

      const updatedTransaction = await updateTransaction(id, updates);

      const response: ApiResponse<Transaction> = {
        success: true,
        data: updatedTransaction,
        message: 'Transaction updated successfully'
      };

      res.json(response);
    } catch (error) {
      console.error('Update transaction error:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update transaction'
      });
    }
  }

  static async deleteTransaction(req: AuthRequest, res: any) {
    try {
      const { id } = req.params as { id: string };


      if (!id) {
        return res.status(400).json({
          success: false,
          error: 'Transaction ID is required'
        });
      }

      await deleteTransaction(id);

      const response: ApiResponse = {
        success: true,
        message: 'Transaction deleted successfully'
      };

      res.json(response);
    } catch (error) {
      console.error('Delete transaction error:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete transaction'
      });
    }
  }

  static async getTransactionStats(req: AuthRequest, res: any) {
    try {
      const userId = req.user!.uid;
      const { startDate, endDate } = req.query as { startDate?: string; endDate?: string };

      const stats = await getTransactionStats(
        userId,
        startDate as string,
        endDate as string
      );

      const response: ApiResponse<TransactionStats> = {
        success: true,
        data: stats,
        message: 'Transaction statistics retrieved successfully'
      };

      res.json(response);
    } catch (error) {
      console.error('Get transaction stats error:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to retrieve transaction statistics'
      });
    }
  }

  static async autoCategorize(req: AuthRequest, res: any) {
    try {
      const userId = req.user!.uid;
      const { transactionIds } = req.body as { transactionIds: string[] };
      if (!transactionIds || !Array.isArray(transactionIds) || transactionIds.length === 0) {
        return res.status(400).json({ success: false, error: 'transactionIds array is required' });
      }
      const updated = await autoCategorizeTransactions(transactionIds, userId);
      res.json({ success: true, data: updated, message: `Auto-categorized ${updated.length} transactions` });
    } catch (error) {
      console.error('Auto-categorize error:', error);
      res.status(500).json({ success: false, error: error instanceof Error ? error.message : 'Failed to auto-categorize transactions' });
    }
  }
} 