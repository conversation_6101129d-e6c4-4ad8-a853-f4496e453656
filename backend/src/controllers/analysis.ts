import { AIService } from '../../services/document-processor/src/ai';
import { addTransactions } from '../../services/document-processor/src/utils/supabase';
import { AuthRequest, AnalysisResponse, Transaction } from '../../services/document-processor/src/types';

export class AnalysisController {
  static async analyzeText(req: AuthRequest, res: any) {
    try {
      const { text, analysisMode = 'advanced' } = req.body as { text: string; analysisMode?: 'basic' | 'advanced' };
      const userId = req.user!.uid;

      if (!text) {
        return res.status(400).json({
          success: false,
          error: 'Text is required'
        });
      }

      // Analyze the text with the specified mode
      const { transactions, detectedBank } = await AIService.analyzeText(text, analysisMode);

      // Add user_id to transactions
      const transactionsWithUserId = transactions.map((tx: Transaction) => ({
        ...tx,
        user_id: userId
      }));

      // Save to database
      const savedTransactions = await addTransactions(transactionsWithUserId);

      const response: AnalysisResponse = {
        success: true,
        transactions: savedTransactions,
        detectedBank,
        message: `Successfully analyzed and saved ${savedTransactions.length} transactions`
      };

      res.json(response);
    } catch (error) {
      console.error('Analysis error:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Analysis failed'
      });
    }
  }

  static async analyzeImage(req: AuthRequest, res: any) {
    try {
      const file = req.file;
      const analysisMode = (req.body.analysisMode as 'basic' | 'advanced') || 'advanced';
      const userId = req.user!.uid;

      if (!file) {
        return res.status(400).json({
          success: false,
          error: 'Image file is required'
        });
      }

      // Check file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
      if (!allowedTypes.includes(file.mimetype)) {
        return res.status(400).json({
          success: false,
          error: 'Only JPEG and PNG images are supported'
        });
      }

      // Analyze the image with the specified mode
      const { transactions, detectedBank } = await AIService.analyzeImage(file.buffer, analysisMode);

      // Add user_id to transactions
      const transactionsWithUserId = transactions.map((tx: Transaction) => ({
        ...tx,
        user_id: userId
      }));

      // Save to database
      const savedTransactions = await addTransactions(transactionsWithUserId);

      const response: AnalysisResponse = {
        success: true,
        transactions: savedTransactions,
        detectedBank,
        message: `Successfully analyzed and saved ${savedTransactions.length} transactions`
      };

      res.json(response);
    } catch (error) {
      console.error('Image analysis error:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Image analysis failed'
      });
    }
  }
} 