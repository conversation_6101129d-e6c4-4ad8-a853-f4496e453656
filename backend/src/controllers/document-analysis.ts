import { DocumentProcessor } from '../../services/document-processor/src/document-processor';
import { addTransactions, getTransactions, getTransactionStats } from '../../services/document-processor/src/utils/supabase';
import { AuthRequest, ProcessingResult, Transaction } from '../../services/document-processor/src/types';

export class DocumentAnalysisController {
  // Process document (receipt, bank statement, etc.)
  static async processDocument(req: AuthRequest, res: any) {
    try {
      const file = req.file;
      const userId = req.user!.uid;

      if (!file) {
        return res.status(400).json({
          success: false,
          error: 'No file uploaded'
        });
      }

      // Validate file type
      const allowedTypes = [
        'image/jpeg', 'image/jpg', 'image/png', 'image/webp', 
        'application/pdf',
        'text/csv', 'application/vnd.ms-excel', 
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      ];
      if (!allowedTypes.includes(file.mimetype)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid file type. Only JPEG, PNG, WebP images, PDF files, CSV files, and Excel files (.xls, .xlsx) are supported.'
        });
      }

      // Validate file size (10MB limit)
      if (file.size > 10 * 1024 * 1024) {
        return res.status(400).json({
          success: false,
          error: 'File too large. Maximum size is 10MB.'
        });
      }

      console.log(`Processing document for user ${userId}: ${file.originalname}`);

      // Get analysis mode from request body
      const analysisMode = (req.body.analysisMode as 'basic' | 'advanced') || 'advanced';
      
      // Process the document with the specified analysis mode
      const result: ProcessingResult = await DocumentProcessor.processDocument(
        file.buffer,
        file.mimetype,
        userId,
        file.originalname,
        { enableAdvancedAnalysis: analysisMode === 'advanced' }
      );

      if (!result.success) {
        return res.status(500).json({
          success: false,
          error: result.error || 'Document processing failed'
        });
      }

      // Save extracted transactions to database
      if (result.extractedData && result.extractedData.transactions.length > 0) {
        try {
          const savedTransactions = await addTransactions(
            result.extractedData.transactions
          );
          
          console.log(`Saved ${savedTransactions.length} transactions to database`);
          
          // Update the result with saved transaction IDs
          result.extractedData.transactions = savedTransactions;
        } catch (dbError) {
          console.error('Failed to save transactions to database:', dbError);
          // Don't fail the entire request if DB save fails
          // The user can still see the extracted data
        }
      }

      return res.json({
        success: true,
        data: {
          processingResult: result,
          message: `Successfully processed ${result.extractedData?.transactions.length || 0} transactions`
        }
      });

    } catch (error) {
      console.error('Document analysis error:', error);
      return res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Internal server error'
      });
    }
  }

  // Get processing history for a user
  static async getProcessingHistory(req: AuthRequest, res: any) {
    try {
      const userId = req.user!.uid;
      
      // This would typically query a separate table for processing history
      // For now, we'll return the user's transactions as a proxy
      const transactions = await getTransactions(userId);
      
      return res.json({
        success: true,
        data: {
          processingHistory: transactions.map((tx: Transaction) => ({
            id: tx.id,
            date: tx.date,
            description: tx.description,
            amount: tx.amount,
            type: tx.type,
            source: tx.source_bank,
            processedAt: tx.created_at
          }))
        }
      });

    } catch (error) {
      console.error('Error fetching processing history:', error);
      return res.status(500).json({
        success: false,
        error: 'Failed to fetch processing history'
      });
    }
  }

  // Validate extracted data before saving
  static async validateExtractedData(req: AuthRequest, res: any) {
    try {
      const { extractedData } = req.body as { extractedData: any };


      if (!extractedData || !extractedData.transactions) {
        return res.status(400).json({
          success: false,
          error: 'Invalid extracted data format'
        });
      }

      // Validate each transaction
      const validationResults = extractedData.transactions.map((tx: any) => {
        const errors: string[] = [];
        
        if (!tx.date) errors.push('Missing date');
        if (!tx.amount || tx.amount <= 0) errors.push('Invalid amount');
        if (!tx.description) errors.push('Missing description');
        if (!tx.type || !['credit', 'debit'].includes(tx.type)) errors.push('Invalid transaction type');
        
        return {
          transaction: tx,
          isValid: errors.length === 0,
          errors
        };
      });

      const validTransactions = validationResults.filter((r: any) => r.isValid);
      const invalidTransactions = validationResults.filter((r: any) => !r.isValid);

      return res.json({
        success: true,
        data: {
          validationResults,
          summary: {
            total: extractedData.transactions.length,
            valid: validTransactions.length,
            invalid: invalidTransactions.length
          }
        }
      });

    } catch (error) {
      console.error('Data validation error:', error);
      return res.status(500).json({
        success: false,
        error: 'Validation failed'
      });
    }
  }

  // Get processing statistics
  static async getProcessingStats(req: AuthRequest, res: any) {
    try {
      const userId = req.user!.uid;
      
      // Get user's transaction stats
      const stats = await getTransactionStats(userId);
      
      // Calculate processing statistics
      const processingStats = {
        totalDocumentsProcessed: stats.transactionCount,
        totalAmountProcessed: stats.totalIncome + stats.totalExpenses,
        averageConfidence: 0.85, // This would come from actual processing history
        lastProcessed: new Date().toISOString(),
        processingSuccessRate: 0.95 // This would be calculated from actual history
      };

      return res.json({
        success: true,
        data: {
          processingStats,
          transactionStats: stats
        }
      });

    } catch (error) {
      console.error('Error fetching processing stats:', error);
      return res.status(500).json({
        success: false,
        error: 'Failed to fetch processing statistics'
      });
    }
  }
} 