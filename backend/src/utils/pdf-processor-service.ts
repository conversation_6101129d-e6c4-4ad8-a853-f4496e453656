import axios from 'axios';
import { ProcessingResult } from '../../services/document-processor/src/types';
import { getCloudRunIdToken } from '../../services/document-processor/src/utils/auth';

/**
 * PDF Processor Service Client
 * 
 * Handles direct communication with the Python PDF processor service
 * This bypasses the document processor service for PDF files
 */
export class PDFProcessorService {
  
  private static readonly PDF_SERVICE_URL = process.env.PDF_SERVICE_URL || 'https://pdf-processor-956727780312.us-central1.run.app';
  private static readonly TIMEOUT = 300000; // 5 minutes
  
  /**
   * Process a PDF file directly using the Python PDF processor service
   */
  static async processPDF(
    fileBuffer: Buffer,
    fileName: string,
    userId: string,
    options: { enableAdvancedAnalysis?: boolean } = {}
  ): Promise<ProcessingResult> {
    
    const startTime = Date.now();
    
    try {
      console.log(`📄 Processing PDF directly with PDF service: ${fileName}`);
      
      // Create form data for the PDF service
      const FormData = require('form-data');
      const form = new FormData();
      form.append('file', fileBuffer, {
        filename: fileName,
        contentType: 'application/pdf'
      });
      form.append('userId', userId);
      
      // Add processing options
      if (options.enableAdvancedAnalysis !== undefined) {
        form.append('enableAdvancedAnalysis', options.enableAdvancedAnalysis.toString());
      }
      
      // Get ID token for Cloud Run authentication
      const audience = this.PDF_SERVICE_URL;
      const idToken = await getCloudRunIdToken(audience);
      
      // Make request to PDF processor service
      const response = await axios.post(
        `${this.PDF_SERVICE_URL}/api/v1/process`,
        form,
        {
          headers: {
            ...form.getHeaders(),
            'X-User-Id': userId,
            'Authorization': `Bearer ${idToken}`
          },
          timeout: this.TIMEOUT,
          maxContentLength: Infinity,
          maxBodyLength: Infinity
        }
      );
      
      const processingTime = Date.now() - startTime;
      
      if (response.data.success) {
        console.log(`✅ PDF processed successfully: ${response.data.data.extraction_method}`);
        
        // Transform PDF service response to match ProcessingResult interface
        return this.transformPDFResponse(response.data.data, processingTime, fileName);
      } else {
        throw new Error(response.data.error || 'PDF processing failed');
      }
      
    } catch (error) {
      const processingTime = Date.now() - startTime;
      console.error(`❌ PDF processing failed for ${fileName}:`, error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'PDF processing failed',
        processingTime,
        batchId: '',
        processingId: ''
      };
    }
  }
  
  /**
   * Transform PDF service response to match the expected ProcessingResult format
   */
  private static transformPDFResponse(
    pdfData: any,
    processingTime: number,
    fileName: string
  ): ProcessingResult {
    
    try {
      // Extract transactions from the PDF service response
      const transactions = pdfData.transactions || [];
      
      // Transform transactions to match expected format
      const formattedTransactions = transactions.map((tx: any) => ({
        id: tx.id || `temp_${Date.now()}_${Math.random()}`,
        date: tx.date,
        description: tx.description || tx.memo || '',
        amount: parseFloat(tx.amount) || 0,
        type: tx.type || (parseFloat(tx.amount) >= 0 ? 'credit' : 'debit'),
        source_bank: tx.source_bank || tx.bank || 'Unknown',
        bank_provided_id: tx.bank_provided_id || tx.bankProvidedId || null,
        category: tx.category || 'Uncategorized',
        user_id: tx.user_id || '',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }));
      
      return {
        success: true,
        extractedData: {
          transactions: formattedTransactions,
          summary: {
            totalTransactions: formattedTransactions.length,
            totalAmount: formattedTransactions.reduce((sum: number, tx: any) => sum + Math.abs(tx.amount), 0),
            dateRange: this.calculateDateRange(formattedTransactions),
            processingMethod: pdfData.extraction_method || 'pdf_processor'
          },
          metadata: {
            fileName,
            fileSize: pdfData.file_size || 0,
            processingTime,
            extractionMethod: pdfData.extraction_method || 'pdf_processor',
            confidence: pdfData.confidence || 0.8,
            pagesProcessed: pdfData.pages_processed || 1
          }
        },
        processingTime,
        batchId: pdfData.batch_id || '',
        processingId: pdfData.processing_id || ''
      };
      
    } catch (error) {
      console.error('Error transforming PDF response:', error);
      
      return {
        success: false,
        error: 'Failed to transform PDF processing results',
        processingTime,
        batchId: '',
        processingId: ''
      };
    }
  }
  
  /**
   * Calculate date range from transactions
   */
  private static calculateDateRange(transactions: any[]): { start: string; end: string } | null {
    if (transactions.length === 0) return null;
    
    const dates = transactions
      .map(tx => new Date(tx.date))
      .filter(date => !isNaN(date.getTime()))
      .sort((a, b) => a.getTime() - b.getTime());
    
    if (dates.length === 0) return null;
    
    return {
      start: dates[0].toISOString(),
      end: dates[dates.length - 1].toISOString()
    };
  }
  
  /**
   * Health check for PDF processor service
   */
  static async healthCheck(): Promise<{ success: boolean; message?: string }> {
    try {
      const response = await axios.get(`${this.PDF_SERVICE_URL}/health`, {
        timeout: 10000 // 10 seconds
      });
      
      return {
        success: response.data.success || false,
        message: response.data.message || 'PDF processor service is running'
      };
      
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'PDF processor service unavailable'
      };
    }
  }
}
