import { DocumentProcessor } from '../../services/document-processor/src/document-processor';
import { PDFProcessorService } from './pdf-processor-service';
import { ProcessingResult } from '../../services/document-processor/src/types';

/**
 * File Type Router - Routes files to appropriate processing services based on file type
 * 
 * This implements the proper architecture where:
 * - PDF files go directly to the PDF processor service
 * - All other file types go to the document processor service
 */
export class FileTypeRouter {
  
  /**
   * Route file processing based on file type
   */
  static async routeFileProcessing(
    fileBuffer: Buffer,
    mimeType: string,
    userId: string,
    fileName: string,
    options: { enableAdvancedAnalysis?: boolean } = {}
  ): Promise<ProcessingResult> {
    
    console.log(`🔀 Routing file: ${fileName} (${mimeType}) for user: ${userId}`);
    
    try {
      // Route PDFs directly to PDF processor service
      if (this.isPDFFile(mimeType, fileName)) {
        console.log(`📄 Routing PDF to PDF processor service: ${fileName}`);
        return await PDFProcessorService.processPDF(
          fileBuffer,
          fileName,
          userId,
          options
        );
      }
      
      // Route all other file types to document processor service
      console.log(`📄 Routing non-PDF to document processor service: ${fileName}`);
      return await DocumentProcessor.processDocument(
        fileBuffer,
        mimeType,
        userId,
        fileName,
        options
      );
      
    } catch (error) {
      console.error(`❌ File routing failed for ${fileName}:`, error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'File processing failed',
        processingTime: 0,
        batchId: '',
        processingId: ''
      };
    }
  }
  
  /**
   * Check if file is a PDF based on MIME type and file extension
   */
  private static isPDFFile(mimeType: string, fileName: string): boolean {
    // Check MIME type first (most reliable)
    if (mimeType === 'application/pdf') {
      return true;
    }
    
    // Fallback to file extension check
    const fileExtension = fileName.toLowerCase().split('.').pop();
    return fileExtension === 'pdf';
  }
  
  /**
   * Get the appropriate processor type for a file
   */
  static getProcessorType(mimeType: string, fileName: string): 'pdf' | 'document' {
    return this.isPDFFile(mimeType, fileName) ? 'pdf' : 'document';
  }
  
  /**
   * Validate that file type is supported by the routing system
   */
  static isSupportedFileType(mimeType: string): boolean {
    const supportedTypes = [
      // PDF files (routed to PDF processor)
      'application/pdf',
      
      // Image files (routed to document processor)
      'image/jpeg',
      'image/jpg', 
      'image/png',
      'image/webp',
      
      // Structured data files (routed to document processor)
      'text/csv',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ];
    
    return supportedTypes.includes(mimeType);
  }
  
  /**
   * Get human-readable description of routing decision
   */
  static getRoutingDescription(mimeType: string, fileName: string): string {
    const processorType = this.getProcessorType(mimeType, fileName);
    
    if (processorType === 'pdf') {
      return `PDF file will be processed by specialized PDF processor service for optimal text extraction and OCR`;
    } else {
      return `Non-PDF file will be processed by document processor service with appropriate format handling`;
    }
  }
}
