#!/usr/bin/env node

/**
 * Database Performance Monitoring Script
 * 
 * This script monitors database performance, analyzes query patterns,
 * and provides optimization recommendations.
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import dotenv from 'dotenv';

dotenv.config();

const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function success(message) {
  log(`✅ ${message}`, colors.green);
}

function error(message) {
  log(`❌ ${message}`, colors.red);
}

function warning(message) {
  log(`⚠️  ${message}`, colors.yellow);
}

function info(message) {
  log(`ℹ️  ${message}`, colors.blue);
}

function header(message) {
  log(`\n${colors.bold}=== ${message} ===${colors.reset}`, colors.blue);
}

// Initialize Supabase client
function initializeSupabase() {
  const supabaseUrl = process.env.SUPABASE_URL;
  const supabaseSecretKey = process.env.SUPABASE_SECRET_KEY;

  if (!supabaseUrl || !supabaseSecretKey) {
    throw new Error('Missing Supabase environment variables');
  }

  return createClient(supabaseUrl, supabaseSecretKey);
}

// Get table sizes and row counts
async function analyzeTableSizes(supabase) {
  header('Table Size Analysis');
  
  const tables = [
    'transactions',
    'processing_logs',
    'audit_trail',
    'batch_metadata',
    'job_status',
    'error_logs',
    'performance_logs'
  ];
  
  const tableStats = [];
  
  for (const tableName of tables) {
    try {
      // Get row count
      const { count, error: countError } = await supabase
        .from(tableName)
        .select('*', { count: 'exact', head: true });
      
      if (countError && countError.code !== 'PGRST116') {
        warning(`Could not get count for ${tableName}: ${countError.message}`);
        continue;
      }
      
      const rowCount = count || 0;
      
      // Estimate size (rough calculation)
      const estimatedSizeKB = Math.round(rowCount * 0.5); // Rough estimate: 0.5KB per row
      
      tableStats.push({
        table: tableName,
        rows: rowCount,
        estimatedSizeKB,
        status: rowCount > 0 ? 'active' : 'empty'
      });
      
      const sizeColor = estimatedSizeKB > 1000 ? colors.yellow : colors.green;
      log(`${tableName}: ${rowCount.toLocaleString()} rows (~${estimatedSizeKB}KB)`, sizeColor);
      
    } catch (err) {
      error(`Failed to analyze ${tableName}: ${err.message}`);
    }
  }
  
  return tableStats;
}

// Analyze query performance patterns
async function analyzeQueryPatterns(supabase) {
  header('Query Pattern Analysis');
  
  try {
    // Test common query patterns and measure performance
    const queryTests = [
      {
        name: 'User Transactions (Recent)',
        query: () => supabase
          .from('transactions')
          .select('*')
          .eq('user_id', 'test-user')
          .gte('date', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString())
          .order('date', { ascending: false })
          .limit(50)
      },
      {
        name: 'Transaction Search',
        query: () => supabase
          .from('transactions')
          .select('*')
          .textSearch('description', 'payment')
          .limit(20)
      },
      {
        name: 'Processing Logs (User)',
        query: () => supabase
          .from('processing_logs')
          .select('*')
          .eq('user_id', 'test-user')
          .order('timestamp', { ascending: false })
          .limit(20)
      },
      {
        name: 'Job Status (Active)',
        query: () => supabase
          .from('job_status')
          .select('*')
          .in('status', ['queued', 'processing'])
          .order('created_at', { ascending: false })
          .limit(10)
      },
      {
        name: 'Error Logs (Recent)',
        query: () => supabase
          .from('error_logs')
          .select('*')
          .gte('timestamp', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString())
          .order('timestamp', { ascending: false })
          .limit(10)
      }
    ];
    
    const performanceResults = [];
    
    for (const test of queryTests) {
      try {
        const startTime = Date.now();
        const { data, error } = await test.query();
        const duration = Date.now() - startTime;
        
        if (error && error.code !== 'PGRST116') {
          warning(`Query failed: ${test.name} - ${error.message}`);
          continue;
        }
        
        const resultCount = data ? data.length : 0;
        const performance = duration < 100 ? 'excellent' : duration < 500 ? 'good' : duration < 1000 ? 'fair' : 'poor';
        const performanceColor = performance === 'excellent' ? colors.green : 
                                performance === 'good' ? colors.blue :
                                performance === 'fair' ? colors.yellow : colors.red;
        
        log(`${test.name}: ${duration}ms (${resultCount} results) - ${performance}`, performanceColor);
        
        performanceResults.push({
          name: test.name,
          duration,
          resultCount,
          performance
        });
        
      } catch (err) {
        error(`Query test failed: ${test.name} - ${err.message}`);
      }
    }
    
    return performanceResults;
    
  } catch (err) {
    error(`Query pattern analysis failed: ${err.message}`);
    return [];
  }
}

// Check index usage and recommendations
async function analyzeIndexUsage(supabase) {
  header('Index Usage Analysis');
  
  const indexRecommendations = [];
  
  try {
    // Check if common queries would benefit from indexes
    const indexChecks = [
      {
        table: 'transactions',
        columns: ['user_id', 'date'],
        reason: 'User transaction queries by date'
      },
      {
        table: 'transactions',
        columns: ['category'],
        reason: 'Category-based filtering'
      },
      {
        table: 'processing_logs',
        columns: ['user_id', 'timestamp'],
        reason: 'User processing history'
      },
      {
        table: 'job_status',
        columns: ['status', 'created_at'],
        reason: 'Job queue monitoring'
      },
      {
        table: 'audit_trail',
        columns: ['user_id', 'timestamp'],
        reason: 'User audit history'
      }
    ];
    
    for (const check of indexChecks) {
      // Test query performance to infer index effectiveness
      const startTime = Date.now();
      
      try {
        let query = supabase.from(check.table).select('*');
        
        // Add filters based on the columns being checked
        if (check.columns.includes('user_id')) {
          query = query.eq('user_id', 'test-user');
        }
        if (check.columns.includes('status')) {
          query = query.eq('status', 'completed');
        }
        if (check.columns.includes('date')) {
          query = query.gte('date', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString());
        }
        if (check.columns.includes('timestamp')) {
          query = query.gte('timestamp', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString());
        }
        
        const { data, error } = await query.limit(10);
        const duration = Date.now() - startTime;
        
        if (error && error.code !== 'PGRST116') {
          continue;
        }
        
        const indexStatus = duration < 100 ? 'optimal' : duration < 500 ? 'good' : 'needs_optimization';
        const statusColor = indexStatus === 'optimal' ? colors.green : 
                           indexStatus === 'good' ? colors.blue : colors.yellow;
        
        log(`${check.table}(${check.columns.join(', ')}): ${duration}ms - ${indexStatus}`, statusColor);
        
        if (indexStatus === 'needs_optimization') {
          indexRecommendations.push({
            table: check.table,
            columns: check.columns,
            reason: check.reason,
            currentPerformance: duration
          });
        }
        
      } catch (err) {
        warning(`Index check failed for ${check.table}: ${err.message}`);
      }
    }
    
    if (indexRecommendations.length > 0) {
      warning('\nIndex Optimization Recommendations:');
      indexRecommendations.forEach(rec => {
        log(`  - CREATE INDEX ON ${rec.table}(${rec.columns.join(', ')}) -- ${rec.reason}`, colors.yellow);
      });
    } else {
      success('All tested queries show good performance');
    }
    
    return indexRecommendations;
    
  } catch (err) {
    error(`Index analysis failed: ${err.message}`);
    return [];
  }
}

// Generate performance recommendations
function generateRecommendations(tableStats, queryResults, indexRecommendations) {
  header('Performance Recommendations');
  
  const recommendations = [];
  
  // Table size recommendations
  const largeTables = tableStats.filter(t => t.estimatedSizeKB > 1000);
  if (largeTables.length > 0) {
    recommendations.push({
      type: 'table_size',
      priority: 'medium',
      message: `Large tables detected: ${largeTables.map(t => t.table).join(', ')}`,
      action: 'Consider implementing data archiving or partitioning'
    });
  }
  
  // Query performance recommendations
  const slowQueries = queryResults.filter(q => q.performance === 'poor' || q.performance === 'fair');
  if (slowQueries.length > 0) {
    recommendations.push({
      type: 'query_performance',
      priority: 'high',
      message: `Slow queries detected: ${slowQueries.map(q => q.name).join(', ')}`,
      action: 'Review and optimize these queries, consider adding indexes'
    });
  }
  
  // Index recommendations
  if (indexRecommendations.length > 0) {
    recommendations.push({
      type: 'indexing',
      priority: 'high',
      message: `Missing indexes detected for ${indexRecommendations.length} query patterns`,
      action: 'Run the database optimization SQL script to add recommended indexes'
    });
  }
  
  // General recommendations
  const totalRows = tableStats.reduce((sum, t) => sum + t.rows, 0);
  if (totalRows > 100000) {
    recommendations.push({
      type: 'maintenance',
      priority: 'medium',
      message: 'Large dataset detected',
      action: 'Implement regular maintenance procedures (VACUUM, ANALYZE)'
    });
  }
  
  // Display recommendations
  if (recommendations.length === 0) {
    success('No performance issues detected. Database is well optimized!');
  } else {
    recommendations.forEach(rec => {
      const priorityColor = rec.priority === 'high' ? colors.red : 
                           rec.priority === 'medium' ? colors.yellow : colors.blue;
      log(`[${rec.priority.toUpperCase()}] ${rec.message}`, priorityColor);
      log(`  Action: ${rec.action}`, colors.reset);
    });
  }
  
  return recommendations;
}

// Generate performance report
function generateReport(tableStats, queryResults, indexRecommendations, recommendations) {
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      totalTables: tableStats.length,
      activeTables: tableStats.filter(t => t.status === 'active').length,
      totalRows: tableStats.reduce((sum, t) => sum + t.rows, 0),
      estimatedTotalSizeKB: tableStats.reduce((sum, t) => sum + t.estimatedSizeKB, 0)
    },
    tableStats,
    queryPerformance: {
      tests: queryResults,
      averageQueryTime: queryResults.reduce((sum, q) => sum + q.duration, 0) / queryResults.length,
      slowQueries: queryResults.filter(q => q.performance === 'poor' || q.performance === 'fair').length
    },
    indexAnalysis: {
      recommendations: indexRecommendations,
      optimizationNeeded: indexRecommendations.length > 0
    },
    recommendations,
    overallHealth: recommendations.filter(r => r.priority === 'high').length === 0 ? 'good' : 'needs_attention'
  };
  
  // Save report
  const reportFile = 'scripts/performance/performance-report.json';
  fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));
  
  return report;
}

// Main monitoring function
async function main() {
  header('FinScope Database Performance Monitor');
  
  try {
    const supabase = initializeSupabase();
    success('Connected to Supabase');
    
    // Run analysis
    const tableStats = await analyzeTableSizes(supabase);
    const queryResults = await analyzeQueryPatterns(supabase);
    const indexRecommendations = await analyzeIndexUsage(supabase);
    
    // Generate recommendations
    const recommendations = generateRecommendations(tableStats, queryResults, indexRecommendations);
    
    // Generate report
    const report = generateReport(tableStats, queryResults, indexRecommendations, recommendations);
    
    header('Performance Summary');
    log(`Total tables: ${report.summary.totalTables}`);
    log(`Active tables: ${report.summary.activeTables}`);
    log(`Total rows: ${report.summary.totalRows.toLocaleString()}`);
    log(`Estimated size: ${Math.round(report.summary.estimatedTotalSizeKB / 1024)}MB`);
    log(`Average query time: ${Math.round(report.queryPerformance.averageQueryTime)}ms`);
    log(`Overall health: ${report.overallHealth}`, 
        report.overallHealth === 'good' ? colors.green : colors.yellow);
    
    info(`Performance report saved to: scripts/performance/performance-report.json`);
    
    if (report.overallHealth === 'good') {
      success('\n🎉 Database performance is optimal!');
    } else {
      warning('\n⚠️  Database performance needs attention. Review recommendations above.');
    }
    
  } catch (err) {
    error(`Performance monitoring failed: ${err.message}`);
    process.exit(1);
  }
}

// Handle script execution
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(err => {
    error(`Performance monitor failed: ${err.message}`);
    console.error(err.stack);
    process.exit(1);
  });
}

export { analyzeTableSizes, analyzeQueryPatterns, analyzeIndexUsage, generateRecommendations };
